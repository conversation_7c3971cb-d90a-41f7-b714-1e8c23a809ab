# 🔍 Quality Control - MEGA Quality Assurance Command

## Command: `/quality-control [action] [target] [--type=test|analyze|debug|validate|compliance|performance|security|cleanup|format] [--depth=L1-L10] [--healthcare]`

### 🎯 **Purpose**

Ultimate quality assurance orchestrator combining comprehensive testing (Vitest/Playwright),
multi-dimensional analysis, intelligent debugging, compliance validation (LGPD/ANVISA/CFM),
performance testing, security scanning, code cleanup, and ultra-fast formatting in a single
comprehensive command.

### 🧠 **Intelligence Integration**

```yaml
QUALITY_INTELLIGENCE:
  activation_triggers:
    - "/quality-control [action]"
    - "/test", "/analyze", "/debug", "/validate", "/compliance", "/performance", "/security", "/cleanup", "/format"
    - "comprehensive quality check", "healthcare validation", "30-second reality check"
  
  context_detection:
    quality_scope: "Testing, analysis, debugging, validation, compliance, performance, security, cleanup, formatting"
    technology_stack: "Auto-detect: React, Vue, Angular, Node.js, Python, Java, Next.js, Supabase"
    complexity_assessment: "L1-L10 based on scope and requirements"
    healthcare_mode: "LGPD/ANVISA/CFM compliance awareness"
    quality_standards: "≥9.5/10 quality enforcement"
```

## 🚀 **Core Quality Actions**

### **1. COMPREHENSIVE TESTING (test)**

```yaml
TESTING_SUITE:
  purpose: "Complete testing framework with healthcare compliance validation"

  unit_testing_vitest:
    - "Healthcare-specific unit tests with Vitest configuration"
    - "Patient data security validation"
    - "LGPD compliance testing"
    - "Medical calculation accuracy verification"
    - "Multi-tenant isolation testing"

  e2e_testing_playwright:
    - "Complete healthcare workflow validation"
    - "Patient journey end-to-end testing"
    - "Doctor workflow automation testing"
    - "Compliance workflow validation"
    - "Cross-browser healthcare compatibility"

  testing_dashboard:
    - "Real-time healthcare testing status"
    - "LGPD/ANVISA/CFM compliance indicators"
    - "Coverage metrics by medical criticality"
    - "Performance benchmarks for medical workflows"

  testing_reports:
    - "Comprehensive HTML/JSON/PDF reports"
    - "Healthcare compliance documentation"
    - "Regulatory audit preparation"
    - "Medical workflow validation summaries"

  testing_watch_mode:
    - "Intelligent file change monitoring"
    - "Healthcare-specific test prioritization"
    - "Real-time compliance validation"
    - "Medical workflow performance tracking"
```

### **2. MULTI-DIMENSIONAL ANALYSIS (analyze)**

```yaml
ANALYSIS_FRAMEWORK:
  purpose: "Universal analysis combining code review, technical assessment, and healthcare compliance"

  code_analysis:
    - "Git diff analysis for healthcare context"
    - "Patient data touchpoint identification"
    - "LGPD compliance validation"
    - "Security vulnerability assessment"
    - "Performance validation for medical workflows"

  technical_analysis:
    - "Architecture assessment and validation"
    - "Scalability and performance bottlenecks"
    - "Integration complexity analysis"
    - "Healthcare technology stack evaluation"

  business_analysis:
    - "Healthcare market positioning assessment"
    - "Clinical workflow optimization analysis"
    - "Regulatory compliance impact evaluation"
    - "Medical practice efficiency improvements"

  investigation_framework:
    - "Systematic evidence-based analysis"
    - "Medical workflow failure investigation"
    - "Patient data anomaly analysis"
    - "Healthcare security incident investigation"

  insights_generation:
    - "Healthcare trend analysis and prediction"
    - "Medical workflow optimization insights"
    - "Patient outcome improvement opportunities"
    - "Clinical efficiency enhancement recommendations"
```

### **3. INTELLIGENT DEBUGGING (debug)**

```yaml
DEBUGGING_SYSTEM:
  purpose: "Universal debugging with healthcare safety protocols"

  healthcare_debugging:
    - "Patient data safety protocols during debugging"
    - "LGPD compliance maintenance during investigation"
    - "Medical workflow integrity preservation"
    - "Healthcare audit trail maintenance"

  multi_stack_debugging:
    - "React/Vue/Angular frontend debugging"
    - "Node.js/Python/Java backend debugging"
    - "Supabase database debugging with RLS validation"
    - "Multi-tenant isolation debugging"

  performance_debugging:
    - "Medical workflow performance investigation"
    - "Patient data access optimization"
    - "Clinical operation efficiency analysis"
    - "Healthcare system scalability debugging"

  security_debugging:
    - "Patient data exposure prevention"
    - "Healthcare authentication debugging"
    - "Medical audit trail integrity validation"
    - "Compliance violation investigation"
```

### **4. 30-SECOND REALITY CHECK (validate)**

```yaml
REALITY_CHECK_VALIDATION:
  purpose: "Mandatory 30-second validation ensuring 'Test changes instead of assuming they work'"

  mandatory_criteria:
    build_validation: "Did I run/build the code successfully?"
    feature_triggering: "Did I trigger the exact feature I changed?"
    result_observation: "Did I see expected results with own observations?"
    edge_case_testing: "Did I test edge cases and error scenarios?"
    performance_validation: "Did I check performance impact?"

  healthcare_criteria:
    lgpd_compliance: "Did I validate LGPD compliance requirements?"
    medical_performance: "Did I verify ≤100ms patient data operations?"
    audit_trail: "Did I ensure audit trail functionality works?"
    accessibility: "Did I validate medical interface accessibility?"
    security_validation: "Did I ensure no healthcare security vulnerabilities?"

  automated_validation:
    - "Complete build process execution"
    - "Feature workflow end-to-end testing"
    - "Healthcare compliance automated checking"
    - "Medical performance benchmark validation"
    - "Security vulnerability scanning"
```

### **5. COMPLIANCE VALIDATION (compliance)**

```yaml
REGULATORY_COMPLIANCE:
  purpose: "Multi-regulatory compliance validation for Brazilian healthcare"

  lgpd_compliance:
    - "Data Controller and Processor identification"
    - "Patient consent management validation"
    - "Data Subject Rights implementation"
    - "Cross-border data transfer compliance"
    - "Data breach notification procedures"

  anvisa_compliance:
    - "Medical device software classification"
    - "Clinical evaluation requirements"
    - "Post-market surveillance validation"
    - "Quality management system compliance"
    - "Risk management validation"

  cfm_compliance:
    - "Medical professional licensing verification"
    - "Telemedicine practice compliance"
    - "Digital prescription requirements"
    - "Medical documentation standards"
    - "Patient confidentiality validation"

  international_standards:
    - "HIPAA Security and Privacy Rules"
    - "GDPR compliance validation"
    - "ISO 27001 security controls"
    - "OWASP security standards"
```

### **6. PERFORMANCE TESTING (performance)**

```yaml
PERFORMANCE_VALIDATION:
  purpose: "Comprehensive performance testing with healthcare-specific requirements"

  healthcare_performance:
    - "≤100ms patient data access validation"
    - "Medical workflow efficiency testing"
    - "Clinical operation performance benchmarks"
    - "Emergency response performance validation"

  load_testing:
    - "Patient portal concurrent user testing"
    - "Medical record access performance"
    - "Healthcare API endpoint stress testing"
    - "Multi-tenant system load validation"

  accessibility_testing:
    - "WCAG 2.1 AA+ compliance validation"
    - "Healthcare-specific accessibility requirements"
    - "Medical professional workflow accessibility"
    - "Patient portal accessibility testing"

  quality_auditing:
    - "TypeScript strict compliance validation"
    - "Code quality metrics assessment"
    - "Healthcare security patterns validation"
    - "Medical workflow maintainability audit"
```

### **7. SECURITY SCANNING (security)**

```yaml
SECURITY_VALIDATION:
  purpose: "Comprehensive security analysis with patient data protection"

  vulnerability_scanning:
    - "OWASP Top 10 validation"
    - "Healthcare-specific security assessment"
    - "Patient data exposure detection"
    - "Medical audit trail security validation"

  data_protection:
    - "PII/PHI identification and classification"
    - "Patient data encryption validation"
    - "Healthcare access control audit"
    - "Medical data retention compliance"

  compliance_security:
    - "LGPD technical safeguards validation"
    - "HIPAA Security Rule compliance"
    - "Healthcare audit trail verification"
    - "Medical incident response testing"

  threat_assessment:
    - "Healthcare threat landscape analysis"
    - "Medical device security validation"
    - "Patient data breach prevention"
    - "Clinical workflow security assessment"
```

### **8. INTELLIGENT CLEANUP (cleanup)**

```yaml
CODE_CLEANUP:
  purpose: "Constant cleanup with duplicate/obsolete code elimination"

  duplicate_detection:
    - "AST-based code similarity analysis"
    - "Healthcare workflow duplication identification"
    - "Medical utility function consolidation"
    - "Patient data handling pattern optimization"

  obsolete_removal:
    - "Dead code elimination with no backwards compatibility"
    - "Unused import and dependency cleanup"
    - "Deprecated healthcare compliance code removal"
    - "Legacy medical workflow pattern elimination"

  path_correction:
    - "Broken import path automatic fixing"
    - "Healthcare module path optimization"
    - "Medical workflow import corrections"
    - "Compliance module path validation"

  system_optimization:
    - "Bundle size optimization for healthcare apps"
    - "Medical workflow performance enhancement"
    - "Patient data operation optimization"
    - "Clinical efficiency code improvements"
```

### **9. OXLINT + DPRINT FORMATTING (format)**

```yaml
OXLINT_DPRINT_FORMATTING:
  purpose: "Lightning-fast linting with oxlint and formatting with dprint + prettier fallback"

  quality_enforcement:
    - "≥9.5/10 quality standards (non-negotiable)"
    - "Subsecond performance formatting"
    - "Maximum TypeScript type safety"
    - "AI-friendly code generation optimization"

  accessibility_compliance:
    - "WCAG 2.1 AA+ compliance for medical interfaces"
    - "Healthcare accessibility pattern enforcement"
    - "Medical device compatibility validation"
    - "Patient portal accessibility optimization"

  healthcare_patterns:
    - "LGPD compliance code patterns"
    - "Medical data handling best practices"
    - "Patient workflow optimization patterns"
    - "Healthcare audit trail code standards"

  security_enforcement:
    - "Patient data security pattern validation"
    - "Healthcare XSS prevention enforcement"
    - "Medical input validation patterns"
    - "Clinical data protection enforcement"
```

## 🔧 **Universal Usage Patterns**

### **Comprehensive Quality Control**

```bash
# Complete quality assurance suite
/quality-control comprehensive --healthcare --depth=L8
# → Full testing + analysis + debugging + compliance + performance + security + cleanup + formatting

# Healthcare-specific quality validation
/quality-control healthcare --lgpd --anvisa --cfm
# → Complete Brazilian healthcare compliance validation

# 30-second reality check
/quality-control reality-check --mandatory
# → Mandatory validation before task completion

# Performance-focused quality control
/quality-control performance --medical-workflows
# → Healthcare performance testing and optimization
```

### **Specific Quality Actions**

```bash
# Comprehensive testing suite
/quality-control test patient-portal --e2e --compliance
# → Complete healthcare testing with compliance validation

# Deep system analysis
/quality-control analyze medical-system --type=investigation --perspective=multiple
# → Multi-perspective healthcare system analysis

# Healthcare debugging with safety protocols
/quality-control debug lgpd-compliance --severity=critical --healthcare
# → Patient data safety debugging

# Multi-regulatory compliance check
/quality-control compliance --regulation=all --audit-level=comprehensive
# → Complete Brazilian + international compliance validation

# Medical performance testing
/quality-control performance patient-data --type=load --users=1000 --healthcare
# → Healthcare performance testing with medical requirements

# Security assessment with patient data focus
/quality-control security patient-data --depth=comprehensive --healthcare
# → Complete healthcare security validation

# Intelligent code cleanup
/quality-control cleanup --auto-fix --healthcare --aggressive
# → Comprehensive code cleanup with healthcare preservation

# Ultra-fast quality formatting
/quality-control format --healthcare --accessibility --security
# → Complete code quality enforcement
```

## 🏥 **Healthcare & Compliance Integration**

```yaml
HEALTHCARE_QUALITY_STANDARDS:
  lgpd_compliance:
    - "Automated patient data protection validation"
    - "Consent management system testing"
    - "Medical audit trail verification"
    - "Healthcare data subject rights validation"

  performance_medical:
    - "≤100ms patient data operation requirements"
    - "Medical workflow efficiency benchmarks"
    - "Clinical decision support performance"
    - "Emergency response system performance"

  security_healthcare:
    - "Medical-grade security scanning"
    - "Patient data encryption validation"
    - "Healthcare access control testing"
    - "Medical device integration security"

  accessibility_medical:
    - "WCAG 2.1 AA+ for medical interfaces"
    - "Healthcare professional workflow accessibility"
    - "Patient portal accessibility testing"
    - "Emergency system accessibility validation"
```

## 🤝 **Agent Orchestration & MCP Integration**

```yaml
AGENT_COORDINATION:
  apex_qa_debugger:
    role: "Primary quality assurance coordinator and healthcare compliance specialist"
    focus: "Testing strategy, quality validation, healthcare compliance, debugging"
    activation: "All quality control operations, healthcare validation"

  apex_researcher:
    role: "Compliance research and healthcare regulation specialist"
    focus: "LGPD/ANVISA/CFM research, medical best practices, regulatory updates"
    activation: "Compliance validation, healthcare research, regulatory documentation"

  apex_dev:
    role: "Implementation and technical quality specialist"
    focus: "Code quality enforcement, performance optimization, security implementation"
    activation: "Technical quality validation, performance testing, security scanning"

  sequential_thinking:
    role: "Complex quality analysis and multi-dimensional reasoning"
    focus: "Quality strategy analysis, compliance reasoning, investigation frameworks"
    activation: "Complex quality decisions, multi-regulatory analysis"

  tool_integration:
    desktop_commander: "File operations, testing execution, performance monitoring"
    context7: "Quality standards, healthcare regulations, technical documentation"
    supabase_mcp: "Database testing, RLS validation, healthcare data compliance"
    oxlint_dprint: "Code linting with oxlint + formatting with dprint + prettier fallback (≥9.5/10)"
```

## 📊 **Quality Standards & Metrics**

```yaml
QUALITY_ENFORCEMENT:
  testing_standards:
    patient_portal: "≥85% coverage with healthcare workflows"
    compliance_modules: "≥90% coverage with regulatory validation"
    medical_calculations: "≥95% coverage with safety-critical validation"
    general_modules: "≥70% coverage with standard business logic"

  performance_targets:
    patient_data_access: "≤100ms for all patient data operations"
    medical_workflows: "≤200ms for clinical operations"
    healthcare_dashboards: "≤2s for medical interface loading"
    emergency_systems: "≤50ms for emergency response functions"

  security_compliance:
    vulnerability_scanning: "0 critical vulnerabilities allowed"
    healthcare_compliance: "100% LGPD/ANVISA/CFM regulatory compliance"
    data_protection: "Complete patient data encryption validation"
    access_control: "Multi-tenant isolation verified"

  code_quality:
    oxlint_dprint_standard: "≥9.5/10 quality score with oxlint + dprint (non-negotiable)"
    type_safety: "Strict TypeScript without 'any' usage"
    accessibility: "WCAG 2.1 AA+ compliance for medical interfaces"
    maintainability: "Clean, optimized, duplicate-free codebase"
```

## 🌐 **Bilingual Support**

### **Portuguese Commands**

- **`/controle-qualidade test`** - Suite de testes com compliance médico
- **`/controle-qualidade analisar`** - Análise multi-dimensional para saúde
- **`/controle-qualidade debug`** - Debug com protocolos de segurança médica
- **`/controle-qualidade compliance`** - Validação LGPD/ANVISA/CFM
- **`/controle-qualidade performance`** - Testes de performance médica
- **`/controle-qualidade seguranca`** - Análise de segurança para dados de pacientes
- **`/controle-qualidade limpeza`** - Limpeza inteligente de código médico
- **`/controle-qualidade formatar`** - Formatação ultra-rápida com compliance

### **English Commands**

- **`/quality-control test`** - Testing suite with medical compliance
- **`/quality-control analyze`** - Multi-dimensional healthcare analysis
- **`/quality-control debug`** - Debugging with medical safety protocols
- **`/quality-control compliance`** - LGPD/ANVISA/CFM validation
- **`/quality-control performance`** - Medical performance testing
- **`/quality-control security`** - Patient data security analysis
- **`/quality-control cleanup`** - Intelligent medical code cleanup
- **`/quality-control format`** - Ultra-fast formatting with compliance

## 🎯 **Success Criteria & Validation**

```yaml
QUALITY_COMPLETION_VALIDATION:
  comprehensive_coverage: "All quality dimensions validated (testing, analysis, debugging, compliance, performance, security, cleanup, formatting)"
  healthcare_compliance: "Complete LGPD/ANVISA/CFM regulatory compliance verified"
  performance_standards: "Medical workflow performance requirements met (≤100ms patient data operations)"
  security_validation: "Healthcare security standards enforced with zero critical vulnerabilities"
  code_quality: "≥9.5/10 quality standards maintained across all modules"
  accessibility_compliance: "WCAG 2.1 AA+ compliance verified for medical interfaces"

MANDATORY_VALIDATION:
  reality_check_passed: "30-second reality check criteria all met"
  build_success: "Complete build and test execution successful"
  functionality_preserved: "All medical workflows functioning correctly"
  compliance_maintained: "Healthcare regulatory requirements preserved"
  performance_verified: "Medical performance benchmarks achieved"
  security_confirmed: "Patient data protection validated"
```

---

## 🚀 **Ready for Complete Quality Control**

**Quality Control Command** activated with comprehensive quality assurance orchestration:

✅ **Comprehensive Testing** - Vitest/Playwright with healthcare compliance validation\
✅ **Multi-Dimensional Analysis** - Code + Technical + Business + Investigation + Insights\
✅ **Intelligent Debugging** - Universal debugging with healthcare safety protocols\
✅ **30-Second Reality Check** - Mandatory validation ensuring tested functionality\
✅ **Compliance Validation** - LGPD/ANVISA/CFM + HIPAA/GDPR/ISO27001 compliance\
✅ **Performance Testing** - Load + Accessibility + Quality auditing with medical requirements\
✅ **Security Scanning** - OWASP + Healthcare-specific security validation\
✅ **Intelligent Cleanup** - Duplicate/obsolete code elimination with healthcare preservation\
✅ **Ultra-Fast Formatting** - Zero-configuration quality enforcement ≥9.5/10

**Healthcare Ready**: Complete Brazilian healthcare compliance + international standards\
**Quality Enforced**: ≥9.5/10 standards across all quality dimensions\
**Performance Validated**: ≤100ms patient data operations guaranteed\
**Security Assured**: Medical-grade security with patient data protection

**Status**: 🟢 **MEGA Quality Control Command** | **Coverage**: Complete Quality Lifecycle |
**Healthcare**: ✅ LGPD/ANVISA/CFM Compliant | **Standards**: ≥9.5/10 Enforced | **Bilingual**: 🇧🇷
🇺🇸
