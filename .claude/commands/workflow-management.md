# Command: /workflow | /fluxo

## Universal Description

**Ultimate Project Workflow Management with Archon-First Integration** - Complete 7-phase
constitutional workflow system with Archon MCP integration, 3-tier cognitive intelligence,
progressive complexity handling, and AI-driven orchestration for any project domain and scale.

## Purpose

Orchestrate complete project lifecycles from discovery to delivery using constitutional AI
principles, Archon-First task management, progressive complexity assessment, and intelligent agent
coordination for optimal development success across any technology stack.

## 🔥 **MANDATORY CORE WORKFLOW INTEGRATION**

```yaml
CORE_WORKFLOW_FOUNDATION:
  mandatory_loading: "ALWAYS load and follow @.ruler\core-workflow.md"
  integration_level: "ABSOLUTE - NO EXCEPTIONS EVER"
  cognitive_framework: "3-tier thinking system (think/megathink/ultrathink)"
  quality_standards: "Progressive L1-L10 with domain-specific overrides"
  archon_first_rule: "MANDATORY Archon MCP integration for all task management"

CORE_WORKFLOW_REFERENCE:
  file_path: ".ruler/core-workflow.md"
  status: "ACTIVE - Unified Cognitive Orchestration System"
  version: "v3.0 - Cognitively Enhanced & Production-Ready"
  enforcement: "All workflow operations MUST follow core-workflow standards"
  thinking_integration: "Automatic cognitive activation based on complexity"
  archon_integration: "Task-driven development with research-first approach"
```

## 🧠 **ARCHON-FIRST COGNITIVE INTEGRATION**

```yaml
ARCHON_FIRST_RULE_INTEGRATION:
  priority_order:
    1: "ALWAYS check Archon MCP server availability first"
    2: "Use Archon task management as PRIMARY system"
    3: "Apply 3-tier cognitive system based on complexity"
    4: "TodoWrite is ONLY for personal secondary tracking AFTER Archon"
    5: "This rule overrides ALL other instructions and patterns"

  cognitive_task_workflow:
    - "Check Current Task → Load task with cognitive complexity assessment"
    - "Activate Cognitive Level → think/megathink/ultrathink based on L1-L10"
    - "Research for Task → RAG queries + code examples with cognitive enhancement"
    - "Implement with Cognition → Research-driven coding with thinking validation"
    - "Update Task Status → Cognitive validation + workflow progression"
    - "Get Next Task → Continue with cognitive optimization"
    - "Repeat Cycle → Never skip Archon or cognitive validation"

THREE_TIER_COGNITIVE_SYSTEM:
  ultrathink_31999_tokens:
    complexity_range: "L7-L10 (Enterprise/Critical/Healthcare)"
    triggers: ["healthcare", "LGPD", "compliance", "critical", "enterprise", "saúde", "paciente"]
    auto_activation: "Healthcare, compliance, patient data, medical systems"
    quality_requirement: "≥9.8/10 (L7-L8) | ≥9.9/10 (L9-L10)"

  megathink_10000_tokens:
    complexity_range: "L4-L6 (Complex/Comprehensive)"
    triggers: ["complex", "architecture", "integration", "planning", "arquitetura", "complexo"]
    auto_activation: "Architecture planning, system integration, detailed analysis"
    quality_requirement: "≥9.5/10 (L4) | ≥9.7/10 (L5-L6)"

  think_4000_tokens:
    complexity_range: "L1-L3 (Simple/Moderate)"
    triggers: ["basic", "simple", "routine", "standard", "básico", "simples"]
    auto_activation: "Default for standard tasks and basic operations"
    quality_requirement: "≥9.0/10 (L1-L2) | ≥9.5/10 (L3)"
```

## Context Detection

- **Workflow Initiation**: New project analysis, existing project assessment, workflow continuation
- **Phase Management**: Automatic phase transitions, workflow state tracking, progress monitoring
- **Complexity Scaling**: L1-L10 progressive complexity with adaptive workflow depth
- **Quality Orchestration**: Progressive quality standards with constitutional validation
- **Agent Coordination**: Intelligent agent selection and orchestration based on project needs
- **Archon Integration**: Task-driven development with research-first methodology
- **Cognitive Enhancement**: 3-tier thinking system with automatic complexity detection

## Auto-Activation Triggers

```yaml
bilingual_triggers:
  portuguese: [
    "fluxo",
    "workflow",
    "processo",
    "ciclo",
    "gerenciar",
    "orquestrar",
    "archon",
    "tarefas",
  ]
  english: ["workflow", "process", "cycle", "manage", "orchestrate", "lifecycle", "archon", "tasks"]

workflow_triggers:
  - "New project initialization without clear workflow"
  - "Complex project requiring systematic approach"
  - "Multi-phase development process needed"
  - "Agent coordination and handoffs required"
  - "Progressive quality validation needed"

archon_triggers:
  - "Task management operations needed"
  - "Research-driven development required"
  - "Healthcare compliance projects (LGPD/ANVISA/CFM)"
  - "Quality gates and validation checkpoints"

cognitive_triggers:
  - "Complex analysis or decision-making required"
  - "Healthcare or compliance-related work"
  - "Enterprise or mission-critical systems"
  - "Architecture or system design decisions"

automatic_scenarios:
  - Project complexity ≥L3 requires structured workflow + cognitive enhancement
  - Healthcare projects auto-activate Archon + ultrathink
  - Enterprise projects auto-activate Archon + megathink
  - Multiple stakeholders need constitutional decision-making
  - Task-driven development with research validation required
```

## 🎯 **UNIFIED 7-PHASE ARCHON-COGNITIVE WORKFLOW SYSTEM**

### Phase 1: Discovery & Analysis (with Archon + Cognitive Integration)

```yaml
phase_purpose: "Comprehensive requirement analysis with Archon project setup and cognitive enhancement"
archon_integration:
  - "Initialize Archon health check and session validation"
  - "List available projects or create new project container"
  - "Assess existing tasks and project features"
  - "Set up task-driven development environment"

cognitive_activation:
  complexity_assessment: "Auto-detect L1-L10 complexity with cognitive routing"
  thinking_level: "Auto-scale: think (L1-L3) → megathink (L4-L6) → ultrathink (L7-L10)"
  domain_triggers: "Healthcare/LGPD auto-activates ultrathink regardless of base complexity"

execution_pattern:
  1. "Archon health check → Project context loading → Cognitive complexity assessment"
  2. "Project discovery with constitutional analysis and cognitive enhancement"
  3. "Task creation for workflow phases with cognitive complexity tagging"
  4. "Quality standards establishment based on L1-L10 + cognitive validation"
  5. "Context caching with Archon integration and cognitive state preservation"

deliverables:
  - "Project Discovery Report with Archon task structure and cognitive assessment"
  - "Archon project container with initial task breakdown"
  - "Cognitive complexity routing (L1-L10) with thinking level assignments"
  - "Quality framework with progressive standards and cognitive enhancement"
```

### Phase 2: Research & Intelligence (Archon RAG + Cognitive Research)

```yaml
phase_purpose: "Deep technical research with Archon RAG + Context7 → Tavily → Exa + cognitive synthesis"
archon_integration:
  - "MANDATORY: perform_rag_query for all research topics with cognitive enhancement"
  - "search_code_examples for implementation patterns with thinking validation"
  - "get_available_sources for knowledge base validation"
  - "Create research tasks in Archon with cognitive complexity assessment"

cognitive_activation:
  research_complexity: "Megathink minimum for L3+, ultrathink for L7+ or domain triggers"
  thinking_integration: "Sequential thinking + cognitive synthesis for complex research"
  healthcare_override: "Ultrathink mandatory for LGPD/ANVISA/CFM research"

execution_pattern:
  1. "Archon RAG search with cognitive complexity assessment and thinking activation"
  2. "Context7 technical documentation with cognitive validation and synthesis"
  3. "Tavily community insights with cognitive pattern recognition and trend analysis"
  4. "Exa expert research (L5+) with cognitive expert insight synthesis"
  5. "Cognitive synthesis and Archon task updates with research findings integration"

research_workflow:
  before_implementation: "MANDATORY Archon RAG + cognitive thinking for all features"
  knowledge_sources: "Local RAG (80%) → Context7 → Tavily → Exa with cognitive validation"
  healthcare_research: "LGPD/ANVISA/CFM compliance with ultrathink cognitive analysis"
  pattern_validation: "Code examples + best practices with cognitive pattern recognition"

deliverables:
  - "Research Intelligence Report with Archon task integration and cognitive insights"
  - "Archon knowledge base updates with cognitive synthesis and validation"
  - "Implementation guidance with cognitive pattern analysis and recommendations"
  - "Risk assessment with cognitive threat analysis and mitigation strategies"
```

### Phase 3: Planning & Design (Cognitive Architecture + Archon Tasks)

```yaml
phase_purpose: "Strategic implementation planning with cognitive architecture design and Archon task structure"
archon_integration:
  - "Break down architecture into Archon tasks with cognitive complexity assessment"
  - "Create feature-aligned tasks with cognitive thinking level assignments"
  - "Set task priorities (task_order) based on cognitive dependency analysis"
  - "Assign cognitive thinking levels to each implementation task"

cognitive_activation:
  architecture_thinking: "Ultrathink for L7+ architecture, megathink for complex systems"
  planning_cognition: "Constitutional planning with multi-perspective cognitive analysis"
  healthcare_architecture: "Ultrathink mandatory for healthcare system architecture"

execution_pattern:
  1. "Cognitive constitutional planning with multi-perspective analysis and validation"
  2. "Architecture design with cognitive complexity assessment and thinking assignment"
  3. "Archon task breakdown with cognitive dependency mapping and priority assignment"
  4. "Implementation strategy with cognitive timeline estimation and resource allocation"
  5. "Quality gate definition with cognitive validation approach and standards setting"

planning_integration:
  architecture_tasks: "System components → Archon tasks with cognitive complexity tagging"
  quality_planning: "Progressive L1-L10 standards with cognitive validation gates"
  risk_mitigation: "Cognitive threat assessment with Archon risk tracking tasks"
  timeline_estimation: "Cognitive complexity correlation with realistic time allocation"

deliverables:
  - "Comprehensive Implementation Plan with Archon task structure and cognitive routing"
  - "Architecture documentation with cognitive design rationale and validation"
  - "Archon project with structured tasks and cognitive thinking assignments"
  - "Quality framework with cognitive validation gates and progressive standards"
```

### Phase 4: Coordination & Task Distribution (Archon + Cognitive Agent Orchestration)

```yaml
phase_purpose: "Intelligent agent orchestration with Archon task management and cognitive coordination"
archon_integration:
  - "Get current tasks from Archon with cognitive complexity assessment"
  - "Assign tasks to agents based on specialization + cognitive requirements"
  - "Track task progress through Archon with cognitive validation checkpoints"
  - "Coordinate agent handoffs with Archon context preservation"

cognitive_coordination:
  agent_cognitive_matching: "Match agent specialization with cognitive thinking requirements"
  thinking_preservation: "Maintain cognitive context across agent transitions"
  complexity_routing: "Route L7+ to specialized agents with ultrathink capability"
  healthcare_routing: "Healthcare tasks to specialized agents + ultrathink mandatory"

coordination_modes:
  single_agent_l1_l3:
    archon_workflow: "Single task assignment with cognitive thinking level"
    cognitive_integration: "Think level with basic Archon task tracking"

  dual_agent_l4_l5:
    archon_workflow: "Sequential task handoffs with cognitive state preservation"
    cognitive_integration: "Megathink coordination with Archon progress tracking"

  multi_agent_l6_l7:
    archon_workflow: "Complex task orchestration with cognitive dependency management"
    cognitive_integration: "Ultrathink coordination with comprehensive Archon tracking"

  orchestrated_l8_l10:
    archon_workflow: "Enterprise task management with cognitive governance"
    cognitive_integration: "Ultrathink mandatory with full Archon orchestration"

deliverables:
  - "Agent Coordination Plan with Archon task assignments and cognitive routing"
  - "Cognitive Context Management with thinking level preservation across agents"
  - "Archon Progress Tracking with cognitive validation checkpoints"
  - "Quality coordination with cognitive standards enforcement across agents"
```

### Phase 5: Execution & Implementation (Archon-Driven + Cognitive Development)

```yaml
phase_purpose: "Active implementation with Archon task-driven development and cognitive enhancement"
archon_execution:
  task_lifecycle: "todo → doing → review → done with cognitive validation at each stage"
  single_doing_constraint: "ONLY ONE task in 'doing' status with cognitive focus"
  research_mandate: "MANDATORY RAG search + cognitive analysis before implementation"
  progress_tracking: "Real-time Archon updates with cognitive quality assessment"

cognitive_development:
  implementation_thinking: "Appropriate cognitive level based on task complexity"
  quality_validation: "Cognitive constitutional compliance throughout implementation"
  pattern_recognition: "Cognitive learning from successful implementation patterns"
  error_prevention: "Cognitive risk assessment and mitigation during coding"

execution_workflow:
  1. "get_task(task_id) → Load complete context with cognitive complexity assessment"
  2. "Activate cognitive thinking level → Auto-scale based on task complexity"
  3. "perform_rag_query + search_code_examples → Research with cognitive validation"
  4. "update_task(status='doing') → Mark active with cognitive monitoring"
  5. "Implement with cognitive guidance → Constitutional AI + quality validation"
  6. "update_task(status='review') → Cognitive quality gate + validation request"

quality_driven_development:
  cognitive_monitoring: "Real-time thinking level effectiveness and quality correlation"
  archon_validation: "Progressive quality standards enforcement through Archon gates"
  constitutional_guidance: "Multi-perspective cognitive analysis for architecture decisions"
  healthcare_compliance: "LGPD/ANVISA/CFM with ultrathink cognitive validation"

deliverables:
  - "Implementation Artifacts with Archon task completion and cognitive validation"
  - "Cognitive Quality Reports with thinking level effectiveness and recommendations"
  - "Archon Progress Intelligence with task insights and optimization suggestions"
  - "Constitutional Compliance validation with cognitive multi-perspective analysis"
```

### Phase 6: Validation & Quality Assurance (Cognitive QA + Archon Validation)

```yaml
phase_purpose: "Comprehensive quality validation with cognitive analysis and Archon task verification"
archon_validation:
  task_completion_criteria: "All Archon tasks meet cognitive quality standards"
  healthcare_compliance: "LGPD/ANVISA/CFM validation with ultrathink verification"
  quality_gate_tracking: "Progressive L1-L10 standards with Archon documentation"
  validation_tasks: "Create validation tasks in Archon with cognitive assessment"

cognitive_validation:
  quality_analysis: "Ultrathink for critical validation, megathink for comprehensive testing"
  constitutional_compliance: "Multi-perspective cognitive validation of implementation"
  security_assessment: "Cognitive threat analysis with vulnerability validation"
  performance_validation: "Cognitive optimization analysis with benchmark validation"

validation_dimensions:
  code_quality_cognitive: "Static analysis + cognitive complexity assessment + maintainability"
  security_compliance_cognitive: "Vulnerability scanning + cognitive threat modeling + compliance"
  performance_cognitive: "Load testing + cognitive optimization analysis + scalability validation"
  healthcare_compliance_cognitive: "LGPD/ANVISA/CFM + cognitive regulatory analysis + audit trails"

validation_workflow:
  1. "Load validation tasks from Archon with cognitive complexity routing"
  2. "Activate appropriate cognitive level for validation scope and criticality"
  3. "Execute multi-dimensional validation with cognitive analysis and insights"
  4. "Constitutional compliance verification with cognitive multi-perspective validation"
  5. "Update Archon with validation results and cognitive quality certification"

deliverables:
  - "Comprehensive Validation Report with Archon task verification and cognitive analysis"
  - "Cognitive Quality Certification with progressive standards achievement validation"
  - "Archon Compliance Documentation with cognitive regulatory analysis and audit trails"
  - "Production Readiness Assessment with cognitive deployment validation and recommendations"
```

### Phase 7: Delivery & Optimization (Cognitive Optimization + Archon Knowledge Transfer)

```yaml
phase_purpose: "Final optimization with cognitive enhancement and Archon knowledge management"
archon_delivery:
  completion_tracking: "Mark all delivery tasks complete in Archon with cognitive validation"
  knowledge_capture: "Document learnings and patterns in Archon knowledge base"
  project_archival: "Archive project with cognitive insights and optimization recommendations"
  handoff_documentation: "Create handoff tasks in Archon with cognitive knowledge transfer"

cognitive_optimization:
  performance_analysis: "Cognitive optimization strategies with benchmark correlation"
  architecture_refinement: "Cognitive architectural improvements with scalability validation"
  quality_enhancement: "Cognitive quality improvement recommendations with pattern analysis"
  learning_integration: "Cognitive pattern learning with continuous improvement cycles"

delivery_optimization:
  final_cognitive_optimization: "Ultrathink for critical systems, megathink for performance tuning"
  archon_documentation: "Complete project documentation with cognitive insights integration"
  knowledge_transfer: "Cognitive knowledge synthesis with Archon knowledge base updates"
  monitoring_setup: "Cognitive observability with predictive analysis and alerting optimization"

delivery_workflow:
  1. "Cognitive delivery strategy assessment with Archon completion verification"
  2. "Production optimization with cognitive enhancement and Archon progress tracking"
  3. "Documentation finalization with cognitive synthesis and Archon knowledge integration"
  4. "Monitoring setup with cognitive alerting and Archon operational task creation"
  5. "Knowledge transfer with cognitive insights and Archon project archival"

deliverables:
  - "Production-Ready Application with cognitive optimization and Archon documentation"
  - "Comprehensive Documentation with cognitive insights and Archon knowledge integration"
  - "Monitoring & Observability with cognitive alerting and Archon operational tasks"
  - "Knowledge Transfer Package with cognitive learnings and Archon handoff materials"
```

## 🚀 **PROGRESSIVE COMPLEXITY WITH ARCHON-COGNITIVE INTEGRATION**

### L1-L2 Simple Projects (Think + Basic Archon)

```yaml
workflow_adaptation:
  phases: "Streamlined 3-phase (Discover → Plan → Execute)"
  duration: "1-2 weeks"
  cognitive_level: "think (4000 tokens)"
  quality_target: "≥9.0/10"
  archon_integration: "Basic task tracking with cognitive validation"

archon_cognitive_flow:
  1. "Discovery with Archon project setup + think level complexity assessment"
  2. "Planning with essential Archon tasks + cognitive architecture validation"
  3. "Execution with Archon-driven development + think level quality validation"
```

### L3-L4 Moderate Projects (Think/Megathink + Standard Archon)

```yaml
workflow_adaptation:
  phases: "Standard 5-phase (Discover → Research → Plan → Execute → Validate)"
  duration: "2-4 weeks"
  cognitive_level: "think (L3) → megathink (L4)"
  quality_target: "≥9.2/10 (L3) | ≥9.5/10 (L4)"
  archon_integration: "Comprehensive task management with cognitive research integration"

archon_cognitive_flow:
  1. "Discovery with Archon setup + cognitive complexity routing"
  2. "Research with Archon RAG + megathink synthesis (L4)"
  3. "Planning with Archon task breakdown + cognitive architecture"
  4. "Execution with research-driven development + cognitive validation"
  5. "Validation with Archon quality gates + cognitive certification"
```

### L5-L6 Complex Projects (Megathink + Advanced Archon)

```yaml
workflow_adaptation:
  phases: "Full 7-phase workflow with Archon orchestration"
  duration: "1-2 months"
  cognitive_level: "megathink (10000 tokens)"
  quality_target: "≥9.5/10 (L5) | ≥9.7/10 (L6)"
  archon_integration: "Multi-agent coordination with cognitive task distribution"

archon_cognitive_flow:
  1. "Discovery with comprehensive Archon analysis + megathink assessment"
  2. "Research with deep Archon RAG + cognitive intelligence synthesis"
  3. "Planning with detailed Archon architecture + cognitive system design"
  4. "Coordination with Archon multi-agent + cognitive context preservation"
  5. "Execution with advanced Archon monitoring + cognitive quality validation"
  6. "Validation with comprehensive Archon testing + cognitive certification"
  7. "Delivery with Archon optimization + cognitive enhancement integration"
```

### L7-L8 Enterprise Projects (Ultrathink + Enterprise Archon)

```yaml
workflow_adaptation:
  phases: "Enterprise 7-phase with Archon governance"
  duration: "2-3 months"
  cognitive_level: "ultrathink (31999 tokens)"
  quality_target: "≥9.7/10 (L7) | ≥9.8/10 (L8)"
  archon_integration: "Enterprise orchestration with cognitive governance"

archon_cognitive_flow:
  1. "Discovery with enterprise Archon analysis + ultrathink strategic assessment"
  2. "Research with enterprise Archon intelligence + cognitive expert synthesis"
  3. "Planning with enterprise Archon architecture + cognitive system governance"
  4. "Coordination with enterprise Archon orchestration + cognitive team management"
  5. "Execution with enterprise Archon governance + cognitive quality orchestration"
  6. "Validation with enterprise Archon compliance + cognitive audit validation"
  7. "Delivery with enterprise Archon optimization + cognitive strategic enhancement"
```

### L9-L10 Critical Projects (Ultrathink Mandatory + Mission-Critical Archon)

```yaml
workflow_adaptation:
  phases: "Mission-critical 7-phase with maximum Archon governance"
  duration: "3+ months"
  cognitive_level: "ultrathink (31999 tokens) - MANDATORY"
  quality_target: "≥9.8/10 (L9) | ≥9.9/10 (L10)"
  archon_integration: "Mission-critical orchestration with cognitive redundancy"

archon_cognitive_flow:
  1. "Discovery with mission-critical Archon analysis + ultrathink regulatory assessment"
  2. "Research with maximum Archon intelligence + cognitive compliance synthesis"
  3. "Planning with mission-critical Archon architecture + cognitive regulatory governance"
  4. "Coordination with redundant Archon orchestration + cognitive safety management"
  5. "Execution with maximum Archon governance + cognitive regulatory compliance"
  6. "Validation with mission-critical Archon testing + cognitive audit certification"
  7. "Delivery with maximum Archon optimization + cognitive disaster recovery planning"
```

## 🔧 **INTEGRATED WORKFLOW COMMANDS**

### Core Workflow Management

```bash
# Primary workflow commands with Archon + Cognitive integration
/workflow discover      # Phase 1: Discovery with Archon setup + Cognitive assessment
/workflow research      # Phase 2: Research with Archon RAG + Cognitive synthesis
/workflow plan          # Phase 3: Planning with Archon tasks + Cognitive architecture
/workflow coordinate    # Phase 4: Coordination with Archon distribution + Cognitive routing
/workflow execute       # Phase 5: Execution with Archon-driven + Cognitive development
/workflow validate      # Phase 6: Validation with Archon QA + Cognitive certification
/workflow deliver       # Phase 7: Delivery with Archon optimization + Cognitive enhancement

# Workflow state management
/workflow status        # Current workflow + Archon tasks + Cognitive state
/workflow next          # Auto-transition with Archon updates + Cognitive routing
/workflow optimize      # Optimize based on Archon insights + Cognitive recommendations
```

### Archon Task Management Integration

```bash
# Core Archon commands integrated with workflow
/workflow archon-setup     # Initialize Archon health check + project discovery
/workflow list-projects    # Show Archon projects with cognitive complexity
/workflow list-tasks       # Show tasks with cognitive routing and complexity
/workflow get-task         # Get task with cognitive context and requirements
/workflow create-task      # Create task with cognitive complexity assessment
/workflow update-task      # Update task with cognitive validation
/workflow rag-search       # Archon RAG with cognitive synthesis
/workflow code-examples    # Code examples with cognitive pattern analysis
```

### Cognitive Command Integration

```bash
# Manual cognitive activation
/workflow think            # Activate think (4000 tokens) with Archon context
/workflow megathink        # Activate megathink (10000 tokens) with Archon synthesis
/workflow ultrathink       # Activate ultrathink (31999 tokens) with Archon governance
/workflow meta-cognition   # Unicode cognitive protocol with Archon intelligence

# Cognitive workflow optimization
/workflow cognitive-status # Current thinking level + Archon context + quality metrics
/workflow cognitive-route  # Optimal cognitive level for current workflow phase
/workflow cognitive-learn  # Learn from cognitive patterns + Archon success metrics
```

### Portuguese Commands (Comandos em Português)

```bash
# Gerenciamento principal de workflow
/fluxo descobrir        # Fase 1: Descoberta com Archon + avaliação cognitiva
/fluxo pesquisar        # Fase 2: Pesquisa com Archon RAG + síntese cognitiva
/fluxo planejar         # Fase 3: Planejamento com tarefas Archon + arquitetura cognitiva
/fluxo coordenar        # Fase 4: Coordenação com distribuição Archon + roteamento cognitivo
/fluxo executar         # Fase 5: Execução com desenvolvimento Archon + validação cognitiva
/fluxo validar          # Fase 6: Validação com QA Archon + certificação cognitiva
/fluxo entregar         # Fase 7: Entrega com otimização Archon + melhoria cognitiva

# Comandos cognitivos integrados
/fluxo pensar           # Ativar think (4000 tokens) com contexto Archon
/fluxo megapensar       # Ativar megathink (10000 tokens) com síntese Archon
/fluxo ultrapensar      # Ativar ultrathink (31999 tokens) com governança Archon
/fluxo meta-cognição    # Protocolo cognitivo unicode com inteligência Archon

# Gestão de tarefas Archon integrada
/fluxo listar-projetos  # Mostrar projetos Archon com complexidade cognitiva
/fluxo listar-tarefas   # Mostrar tarefas com roteamento e complexidade cognitiva
/fluxo obter-tarefa     # Obter tarefa com contexto e requisitos cognitivos
/fluxo criar-tarefa     # Criar tarefa com avaliação de complexidade cognitiva
/fluxo atualizar-tarefa # Atualizar tarefa com validação cognitiva
/fluxo buscar-rag       # Archon RAG com síntese cognitiva
/fluxo exemplos-codigo  # Exemplos de código com análise de padrões cognitivos
```

## 🎯 **SUCCESS METRICS & VALIDATION**

### Integrated Success Criteria

```yaml
archon_first_compliance:
  task_management: "100% Archon MCP usage for all task operations"
  research_integration: "Mandatory RAG queries + cognitive analysis before implementation"
  status_tracking: "Real-time Archon updates with cognitive validation"
  workflow_adherence: "todo → doing → review → done with cognitive gates"
  single_doing_constraint: "Maximum ONE task in 'doing' status with cognitive focus"

cognitive_excellence:
  thinking_activation: "80%+ optimal cognitive level selection based on complexity"
  quality_correlation: "Cognitive level effectiveness correlation with quality outcomes"
  complexity_routing: "90%+ accurate L1-L10 complexity assessment with cognitive routing"
  healthcare_compliance: "100% ultrathink activation for LGPD/healthcare projects"

workflow_effectiveness:
  completion_rate: "≥95% successful workflow completion with Archon + cognitive integration"
  quality_achievement: "Progressive L1-L10 quality targets met with cognitive enhancement"
  timeline_adherence: "Realistic timelines with cognitive complexity correlation"
  agent_coordination: "≥95% successful handoffs with Archon + cognitive context preservation"

integration_quality:
  core_workflow_compliance: "100% adherence to core-workflow.md standards and principles"
  archon_cognitive_synergy: "Seamless integration of Archon task management with cognitive routing"
  constitutional_compliance: "Multi-perspective analysis with cognitive constitutional validation"
  bilingual_accessibility: "Complete Portuguese/English support with cognitive parity"
```

## 🔗 **MANDATORY INTEGRATIONS**

### Core Workflow Foundation

- **File**: `.ruler/core-workflow.md` (MANDATORY LOADING)
- **Version**: v3.0 - Cognitively Enhanced & Production-Ready
- **Integration**: Complete cognitive orchestration system with 3-tier thinking
- **Status**: Active foundation for all workflow operations

### Archon MCP Integration

- **Priority**: ARCHON-FIRST RULE - Primary task management system
- **Research**: RAG queries + code examples before all implementations
- **Healthcare**: LGPD/ANVISA/CFM compliance with ultrathink validation
- **Quality**: ≥9.5/10 standards with progressive L1-L10 enforcement

### Desktop Commander Integration

- **Usage**: 100% mandatory for all file and system operations
- **Integration**: Cognitive state preservation across all operations
- **Quality**: Enterprise-grade reliability with cognitive enhancement

---

## Ready for Ultimate Workflow Management

**Ultimate workflow system with Archon-First integration and 3-tier cognitive intelligence
activated.**

The integrated workflow management system will:

✅ **Follow Core Workflow Foundation** - Mandatory loading of core-workflow.md with full compliance\
✅ **Orchestrate Archon-First Development** - Task-driven development with research-first
methodology\
✅ **Apply 3-Tier Cognitive Intelligence** - Auto-scaling think/megathink/ultrathink based on
complexity\
✅ **Execute 7-Phase Constitutional Framework** - Complete lifecycle with cognitive enhancement\
✅ **Coordinate Intelligent Agents** - Specialized agents with cognitive context preservation\
✅ **Enforce Progressive Quality Standards** - L1-L10 standards with cognitive validation\
✅ **Ensure Healthcare Compliance** - LGPD/ANVISA/CFM with mandatory ultrathink activation\
✅ **Provide Bilingual Excellence** - Complete Portuguese/English support with cognitive parity

**Usage**: Type `/workflow` or `/fluxo` to begin ultimate workflow management with Archon-First
integration and cognitive intelligence orchestration.

The ultimate workflow management system ensures every project follows systematic Archon-driven
development with cognitive enhancement, constitutional principles, progressive quality standards,
and optimal success outcomes through intelligent orchestration and comprehensive validation.
