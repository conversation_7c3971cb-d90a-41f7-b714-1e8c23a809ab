// Batch Prediction Extended Constants

const MAGI<PERSON>_NUMBERS = {
  FIFTY: 50,
  FORTY_EIGHT: 48,
  ONE: 1,
  ONE_HUNDRED: 100,
  SEVEN: 7,
  SIXTY: 60,
  TEN: 10,
  THIRTY: 30,
  TWENTY_FOUR: 24,
  TWO_HUNDRED: 200,
  ZERO: 0,
} as const;

const DATE_UTILS = {
  HOURS_PER_DAY: 24,
  MILLISECONDS_PER_HOUR: 3_600_000,
  MILLISECONDS_PER_MINUTE: 60_000,
  MILLISECONDS_PER_SECOND: 1000,
  MINUTES_PER_HOUR: 60,
  SECONDS_PER_MINUTE: 60,
} as const;

const BATCH_LIMITS = {
  DEFAULT_BATCH_SIZE_LARGE: 200,
  DEFAULT_BATCH_SIZE_MEDIUM: 100,
  DEFAULT_BATCH_SIZE_SMALL: 50,
  MAX_JOBS: 10,
  MIN_JOBS: 1,
  RECENT_ACTIVITY_LIMIT: 10,
} as const;

const ANALYTICS_DEFAULTS = {
  DAYS_BACK: 30,
  RISK_THRESHOLD_HIGH: 0.7,
  RISK_THRESHOLD_LOW: 0.3,
  SUCCESS_RATE_MULTIPLIER: 100,
} as const;

export { ANALYTICS_DEFAULTS, BATCH_LIMITS, DATE_UTILS, MAGIC_NUMBERS };
