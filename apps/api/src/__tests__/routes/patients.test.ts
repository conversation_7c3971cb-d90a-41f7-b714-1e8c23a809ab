/**
 * 👥 Patient Management API Tests - NeonPro Healthcare Backend
 * ==========================================================
 *
 * Comprehensive API tests for patient management with:
 * - CRUD operations with LGPD compliance
 * - Brazilian healthcare data validation (CPF, RG, CNS)
 * - Multi-tenant data isolation
 * - Medical data privacy and security
 * - Emergency patient handling
 */

import type { Context } from "hono";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

// Mock services and utilities
const mockPatientService = {
  create: vi.fn(),
  findById: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  search: vi.fn(),
  list: vi.fn(),
  validateCPF: vi.fn(),
  validateCNS: vi.fn(),
};

const mockPrisma = {
  patient: {
    create: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn(),
  },
  auditLog: {
    create: vi.fn(),
  },
  lgpdConsent: {
    create: vi.fn(),
    findFirst: vi.fn(),
  },
};

const mockLGPDService = {
  validateConsent: vi.fn(),
  maskSensitiveData: vi.fn(),
  logDataAccess: vi.fn(),
};

describe("patient Management API - NeonPro Healthcare", () => {
  // Mock patient data
  const mockPatient = {
    id: "patient-123",
    tenantId: "clinic-abc",
    name: "Maria da Silva Santos",
    cpf: "12345678900",
    rg: "123456789",
    cns: "123456789012345",
    dateOfBirth: "1985-03-15",
    gender: "FEMALE",
    email: "<EMAIL>",
    phone: "11999999999",
    bloodType: "A+",
    allergies: ["Penicilina"],
    chronicConditions: ["Diabetes"],
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });
  describe("pOST /patients - Create Patient", () => {
    it("should create patient with valid Brazilian healthcare data", async () => {
      const newPatientData = {
        name: "João da Silva",
        cpf: "98765432100",
        rg: "987654321",
        cns: "987654321098765",
        dateOfBirth: "1990-05-20",
        gender: "MALE",
        email: "<EMAIL>",
        phone: "11988888888",
        tenantId: "clinic-abc",
        lgpdConsent: {
          dataProcessing: true,
          marketingCommunications: false,
          dataSharing: false,
          consentDate: new Date().toISOString(),
        },
      };

      mockPatientService.validateCPF.mockReturnValue(true);
      mockPatientService.validateCNS.mockReturnValue(true);
      mockLGPDService.validateConsent.mockResolvedValue({ isValid: true });
      mockPrisma.patient.create.mockResolvedValue({
        ...mockPatient,
        ...newPatientData,
        id: "new-patient-123",
      });

      const mockContext = {
        req: {
          json: vi.fn().mockResolvedValue(newPatientData),
        },
        json: vi.fn(),
        get: vi
          .fn()
          .mockReturnValue({ userId: "doctor-123", tenantId: "clinic-abc" }),
      } as unknown as Context;

      const createPatientHandler = async (c: Context) => {
        const patientData = await c.req.json();
        const { userId, tenantId } = c.get("user");

        // Validate CPF
        if (!mockPatientService.validateCPF(patientData.cpf)) {
          return c.json({ error: "CPF inválido" }, 400);
        }

        // Validate CNS if provided
        if (
          patientData.cns
          && !mockPatientService.validateCNS(patientData.cns)
        ) {
          return c.json({ error: "CNS inválido" }, 400);
        }

        // Validate LGPD consent
        const consentValidation = await mockLGPDService.validateConsent(
          patientData.lgpdConsent,
        );
        if (!consentValidation.isValid) {
          return c.json({ error: "Consentimento LGPD obrigatório" }, 400);
        }

        // Create patient with tenant isolation
        const patient = await mockPrisma.patient.create({
          data: { ...patientData, tenantId },
        });

        // Create audit log
        await mockPrisma.auditLog.create({
          data: {
            action: "PATIENT_CREATED",
            userId,
            tenantId,
            resourceId: patient.id,
            metadata: { patientName: patient.name },
            timestamp: new Date(),
          },
        });

        return c.json({
          success: true,
          data: { patient },
          message: "Paciente criado com sucesso",
        });
      };

      await createPatientHandler(mockContext);

      expect(mockPatientService.validateCPF).toHaveBeenCalledWith(
        "98765432100",
      );
      expect(mockPatientService.validateCNS).toHaveBeenCalledWith(
        "987654321098765",
      );
      expect(mockLGPDService.validateConsent).toHaveBeenCalled();
      expect(mockPrisma.patient.create).toHaveBeenCalledWith({
        data: { ...newPatientData, tenantId: "clinic-abc" },
      });
      expect(mockPrisma.auditLog.create).toHaveBeenCalled();
    });

    it("should reject patient creation with invalid CPF", async () => {
      const invalidPatientData = {
        name: "Test Patient",
        cpf: "12345678999", // Invalid CPF
        email: "<EMAIL>",
        tenantId: "clinic-abc",
      };

      mockPatientService.validateCPF.mockReturnValue(false);

      const mockContext = {
        req: {
          json: vi.fn().mockResolvedValue(invalidPatientData),
        },
        json: vi.fn(),
        get: vi
          .fn()
          .mockReturnValue({ userId: "doctor-123", tenantId: "clinic-abc" }),
      } as unknown as Context;

      const createPatientHandler = async (c: Context) => {
        const patientData = await c.req.json();

        if (!mockPatientService.validateCPF(patientData.cpf)) {
          await mockPrisma.auditLog.create({
            data: {
              action: "PATIENT_CREATION_FAILED",
              userId: c.get("user").userId,
              tenantId: c.get("user").tenantId,
              metadata: { reason: "INVALID_CPF", cpf: patientData.cpf },
              timestamp: new Date(),
            },
          });

          return c.json(
            {
              success: false,
              error: "CPF inválido",
            },
            400,
          );
        }

        return c.json({ success: true });
      };

      await createPatientHandler(mockContext);

      expect(mockPatientService.validateCPF).toHaveBeenCalledWith(
        "12345678999",
      );
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: {
          action: "PATIENT_CREATION_FAILED",
          userId: "doctor-123",
          tenantId: "clinic-abc",
          metadata: { reason: "INVALID_CPF", cpf: "12345678999" },
          timestamp: expect.any(Date),
        },
      });
    });

    it("should require LGPD consent for patient creation", async () => {
      const patientWithoutConsent = {
        name: "Patient Test",
        cpf: "12345678900",
        email: "<EMAIL>",
        lgpdConsent: {
          dataProcessing: false, // Required consent missing
          marketingCommunications: false,
          dataSharing: false,
        },
      };

      mockPatientService.validateCPF.mockReturnValue(true);
      mockLGPDService.validateConsent.mockResolvedValue({
        isValid: false,
        reason: "DATA_PROCESSING_CONSENT_REQUIRED",
      });

      const mockContext = {
        req: {
          json: vi.fn().mockResolvedValue(patientWithoutConsent),
        },
        json: vi.fn(),
      } as unknown as Context;

      const createPatientHandler = async (c: Context) => {
        const patientData = await c.req.json();

        const consentValidation = await mockLGPDService.validateConsent(
          patientData.lgpdConsent,
        );
        if (!consentValidation.isValid) {
          return c.json(
            {
              success: false,
              error: "Consentimento para processamento de dados é obrigatório (LGPD)",
              code: "LGPD_CONSENT_REQUIRED",
            },
            400,
          );
        }

        return c.json({ success: true });
      };

      await createPatientHandler(mockContext);

      expect(mockLGPDService.validateConsent).toHaveBeenCalledWith(
        patientWithoutConsent.lgpdConsent,
      );
    });
  });
  describe("gET /patients/:id - Get Patient", () => {
    it("should retrieve patient with data masking based on user role", async () => {
      const fullPatientData = { ...mockPatient };
      const maskedPatientData = {
        ...mockPatient,
        cpf: "***.***.***-00",
        rg: "**.***.***-*",
        phone: "(**) ****-9999",
      };

      mockPrisma.patient.findUnique.mockResolvedValue(fullPatientData);
      mockLGPDService.maskSensitiveData.mockReturnValue(maskedPatientData);

      const mockContext = {
        req: {
          param: vi.fn().mockReturnValue("patient-123"),
        },
        json: vi.fn(),
        get: vi.fn().mockReturnValue({
          userId: "nurse-123",
          role: "NURSE",
          tenantId: "clinic-abc",
        }),
      } as unknown as Context;

      const getPatientHandler = async (c: Context) => {
        const patientId = c.req.param("id");
        const { userId, role, tenantId } = c.get("user");

        // Find patient with tenant isolation
        const patient = await mockPrisma.patient.findUnique({
          where: {
            id: patientId,
            tenantId,
            isActive: true,
          },
        });

        if (!patient) {
          return c.json({ error: "Paciente não encontrado" }, 404);
        }

        // Apply data masking based on user role
        const responseData = role === "NURSE"
          ? mockLGPDService.maskSensitiveData(patient, role)
          : patient;

        // Log data access
        await mockLGPDService.logDataAccess({
          userId,
          action: "PATIENT_VIEWED",
          patientId: patient.id,
          tenantId,
          dataAccessed: Object.keys(responseData),
        });

        return c.json({
          success: true,
          data: { patient: responseData },
        });
      };

      await getPatientHandler(mockContext);

      expect(mockPrisma.patient.findUnique).toHaveBeenCalledWith({
        where: {
          id: "patient-123",
          tenantId: "clinic-abc",
          isActive: true,
        },
      });
      expect(mockLGPDService.maskSensitiveData).toHaveBeenCalledWith(
        fullPatientData,
        "NURSE",
      );
      expect(mockLGPDService.logDataAccess).toHaveBeenCalled();
    });

    it("should enforce tenant isolation for patient access", async () => {
      mockPrisma.patient.findUnique.mockResolvedValue(); // Patient not found in user's tenant

      const mockContext = {
        req: {
          param: vi.fn().mockReturnValue("patient-456"),
        },
        json: vi.fn(),
        get: vi.fn().mockReturnValue({
          userId: "doctor-123",
          tenantId: "clinic-abc",
        }),
      } as unknown as Context;

      const getPatientHandler = async (c: Context) => {
        const patientId = c.req.param("id");
        const { tenantId } = c.get("user");

        const patient = await mockPrisma.patient.findUnique({
          where: {
            id: patientId,
            tenantId,
            isActive: true,
          },
        });

        if (!patient) {
          return c.json(
            {
              success: false,
              error: "Paciente não encontrado",
            },
            404,
          );
        }

        return c.json({ success: true, data: { patient } });
      };

      await getPatientHandler(mockContext);

      expect(mockPrisma.patient.findUnique).toHaveBeenCalledWith({
        where: {
          id: "patient-456",
          tenantId: "clinic-abc",
          isActive: true,
        },
      });
    });
  });

  describe("pUT /patients/:id - Update Patient", () => {
    it("should update patient with audit trail", async () => {
      const updateData = {
        phone: "11977777777",
        email: "<EMAIL>",
        allergies: ["Penicilina", "Dipirona"],
      };

      const updatedPatient = { ...mockPatient, ...updateData };
      mockPrisma.patient.update.mockResolvedValue(updatedPatient);

      const mockContext = {
        req: {
          param: vi.fn().mockReturnValue("patient-123"),
          json: vi.fn().mockResolvedValue(updateData),
        },
        json: vi.fn(),
        get: vi
          .fn()
          .mockReturnValue({ userId: "doctor-123", tenantId: "clinic-abc" }),
      } as unknown as Context;

      const updatePatientHandler = async (c: Context) => {
        const patientId = c.req.param("id");
        const updateData = await c.req.json();
        const { userId, tenantId } = c.get("user");

        // Update with tenant isolation
        const patient = await mockPrisma.patient.update({
          where: {
            id: patientId,
            tenantId,
          },
          data: updateData,
        });

        // Create detailed audit log
        await mockPrisma.auditLog.create({
          data: {
            action: "PATIENT_UPDATED",
            userId,
            tenantId,
            resourceId: patientId,
            metadata: {
              updatedFields: Object.keys(updateData),
              changes: updateData,
            },
            timestamp: new Date(),
          },
        });

        return c.json({
          success: true,
          data: { patient },
          message: "Paciente atualizado com sucesso",
        });
      };

      await updatePatientHandler(mockContext);

      expect(mockPrisma.patient.update).toHaveBeenCalledWith({
        where: { id: "patient-123", tenantId: "clinic-abc" },
        data: updateData,
      });
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: {
          action: "PATIENT_UPDATED",
          userId: "doctor-123",
          tenantId: "clinic-abc",
          resourceId: "patient-123",
          metadata: {
            updatedFields: ["phone", "email", "allergies"],
            changes: updateData,
          },
          timestamp: expect.any(Date),
        },
      });
    });
  });

  describe("dELETE /patients/:id - Delete Patient (LGPD Right to Erasure)", () => {
    it("should soft delete patient with LGPD compliance", async () => {
      mockPrisma.patient.update.mockResolvedValue({
        ...mockPatient,
        isActive: false,
        deletedAt: new Date(),
      });

      const mockContext = {
        req: {
          param: vi.fn().mockReturnValue("patient-123"),
        },
        json: vi.fn(),
        get: vi
          .fn()
          .mockReturnValue({ userId: "admin-123", tenantId: "clinic-abc" }),
      } as unknown as Context;

      const deletePatientHandler = async (c: Context) => {
        const patientId = c.req.param("id");
        const { userId, tenantId } = c.get("user");

        // Soft delete (LGPD compliance - retain audit trail)
        // Create LGPD erasure audit log
        await mockPrisma.auditLog.create({
          data: {
            action: "PATIENT_DELETED_LGPD",
            userId,
            tenantId,
            resourceId: patientId,
            metadata: {
              reason: "RIGHT_TO_ERASURE",
              dataAnonymized: true,
              auditTrailRetained: true,
            },
            timestamp: new Date(),
          },
        });

        return c.json({
          success: true,
          message: "Paciente removido conforme LGPD",
          data: {
            lgpdCompliance: {
              dataErased: true,
              auditRetained: true,
              erasureDate: new Date().toISOString(),
            },
          },
        });
      };

      await deletePatientHandler(mockContext);

      expect(mockPrisma.patient.update).toHaveBeenCalledWith({
        where: { id: "patient-123", tenantId: "clinic-abc" },
        data: {
          isActive: false,
          deletedAt: expect.any(Date),
          name: "ANONYMOUS",
          cpf: undefined,
          rg: undefined,
          email: undefined,
          phone: undefined,
        },
      });
    });
  });
});
