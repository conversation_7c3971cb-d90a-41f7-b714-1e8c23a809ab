---
type: "always_apply"
---

# Development Workflow

> **🤖 AI Instructions:** This is the mandatory step-by-step process you MUST follow when developing features, unless the user explicitly requests to skip specific steps. Always follow this workflow systematically.

## Pre-Development Guidelines

**📚 Documentation Consultation:**
⚠️ **IMPORTANT**: Only consult documentation when you have specific questions or uncertainties. Avoid loading unnecessary context.

When you have ANY doubt during development:

- First consult the `/docs` folder for relevant documentation
- Check `docs/architecture.md` for architectural decisions
- Review `docs/tech-stack.md` for technology guidelines
- Look at `docs/apis.md` for API patterns
- Check `docs/database-schema.md` for data structure
- Consult specific guides in `/rules` or `/docs` for coding standards, best practices, and design patterns

## 🤖 AGENT COORDINATION SYSTEM

**🎯 Filosofia de Coordenação**: _"Roteamento inteligente para o agente certo na hora certa"_

### **🔄 Sistema de Roteamento Inteligente**

**Coordenador Central** (Sempre Ativo):

- **💻 @apex-dev** - Coordenador base + desenvolvimento full-stack healthcare

**Especialistas On-Demand** (Ativação por Contexto):

- **🔬 @apex-researcher** - Pesquisa multi-fonte e validação constitucional
- **🎨 @apex-ui-ux-designer** - Design healthcare com acessibilidade WCAG 2.1 AA+
- **🏗️ @architect** - Arquitetura de sistemas e design patterns
- **🔒 @audit** - Auditoria de segurança e vulnerabilidades
- **📋 @prd** - Especificações de produto e roadmaps
- **🔧 @refactor** - Refatoração e otimização de código
- **📢 @briefing** - Estratégia de marketing e posicionamento
- **📚 @documentation** - Documentação técnica e guias
- **⚖️ @rules** - Regras e padrões de desenvolvimento

### **🎯 Comandos de Ativação**

```bash
# Agente Único
@apex-dev "implementar autenticação JWT"
@architect "projetar arquitetura de microserviços"
@apex-researcher "validar padrões de segurança LGPD"

# Múltiplos Agentes
@apex-dev,architect "implementar sistema de agendamento"
@apex-ui-ux-designer,apex-dev "criar dashboard responsivo"

# Equipes Completas
@team-full "desenvolver plataforma de telemedicina completa"
@team-audit "revisar segurança e compliance do sistema"
@team-docs "criar documentação técnica e de usuário"
```

### **🔄 Workflows de Colaboração Predefinidos**

**🚀 Desenvolvimento Completo de Feature**:

```yaml
sequence: architect → apex-researcher → apex-dev → apex-ui-ux-designer → audit → documentation
output: "Production-ready healthcare feature with full documentation"
```

**🔬 Pesquisa e Implementação**:

```yaml
sequence: apex-researcher → architect → apex-dev → audit
output: "Evidence-based implementation with security validation"
```

**🔧 Refatoração Segura**:

```yaml
sequence: audit → refactor → apex-dev → audit
output: "Improved codebase with maintained security standards"
```

### **📋 MVP Completo**:

```yaml
sequence: prd → architect → apex-researcher → apex-dev → apex-ui-ux-designer → audit → documentation
output: "Production-ready MVP with full compliance"
```

### **🎯 Matriz de Decisão para Seleção de Agentes**

| Contexto/Trigger        | Agente Principal     | Agentes Colaboradores            | Workflow                      |
| ----------------------- | -------------------- | -------------------------------- | ----------------------------- |
| 🔬 **Pesquisa/Análise** | @apex-researcher     | @architect, @apex-dev            | Research → Implementation     |
| 💻 **Desenvolvimento**  | @apex-dev            | @architect, @audit               | Plan → Code → Validate        |
| 🎨 **UI/UX Design**     | @apex-ui-ux-designer | @apex-dev, @audit                | Design → Implement → Test     |
| 🏗️ **Arquitetura**       | @architect           | @apex-researcher, @apex-dev      | Research → Design → Implement |
| 🔒 **Segurança**        | @audit               | @apex-dev, @refactor             | Audit → Fix → Re-audit        |
| 📋 **Produto**          | @prd                 | @architect, @apex-ui-ux-designer | Spec → Design → Develop       |
| 🔧 **Refatoração**      | @refactor            | @audit, @apex-dev                | Audit → Refactor → Validate   |
| 📚 **Documentação**     | @documentation       | @apex-dev, @architect            | Code → Document → Review      |
| 📢 **Marketing**        | @briefing            | @apex-ui-ux-designer, @prd       | Strategy → Design → Content   |
| ⚖️ **Padrões**           | @rules               | @audit, @apex-dev                | Define → Implement → Enforce  |

**🔄 Regras de Ativação Automática**:

- **Complexidade ≥7**: Ativação automática de @apex-researcher
- **UI/UX necessário**: Auto-inclusão de @apex-ui-ux-designer
- **Segurança crítica**: Auto-inclusão de @audit
- **Arquitetura nova**: Auto-inclusão de @architect

## 🎯 CORE PRINCIPLES & MISSION

```yaml
CORE_STANDARDS:
  mantra: "Think → Research → Decompose → Plan → Implement → Validate"
  mission: "Research first, think systematically, implement flawlessly with cognitive intelligence"
  research_driven: "Multi-source validation for all complex implementations"
  research_excellence: "Context7 → Tavily → Archon → Sequential Thinking chain"
  agent_coordination: "Intelligent routing with apex-dev as central coordinator"
  cognitive_authority: "Automated thinking activation with complexity-based routing"
  vibecoder_integration: "Constitutional excellence with one-shot resolution philosophy"
  core_principle: "Simple systems that work over complex systems that don't"
  archon_first_rule: "MANDATORY Archon MCP integration for all task management"
  mandatory_execution_rules:
    right_tool_for_job: "Understand full context before implementation and choose the right mcp and agent for the job"
    serena_mcp: "MANDATORY use of serena mcp to search codebase, *DO NOT USE* `NATIVE SEARCH CODEBASE`"
    serena_mcp_integration: "MANDATORY use of serena mcp for semantic code analysis"
    supabase_integration: "MANDATORY use of supabase mcp for database operations"
    shadcn-ui_integration: "MANDATORY use of shadcn-ui mcp to manage ui components"
    no_interruptions: "Continue through ALL steps until problem fully solved"
    mandatory_first_step: "Always begin with sequential-thinking tool"
    only_terminate_when: "User query COMPLETELY resolved and Problem 100% solved"
    complete_execution: "Execute entire workflow without interruption"

WORKFLOW_MANAGEMENT_PRINCIPLES:
  preserve_context: "Maintain complete context across all agent and thinking transitions"
  incremental_excellence: "Build quality progressively through workflow phases with cognitive enhancement"
  pattern_optimization: "Learn from successful workflows and apply cognitive improvements"
  scalable_coordination: "Scale from single-agent to multi-agent based on complexity requirements"
  adaptive_routing: "Dynamic agent and thinking selection based on task requirements and domain"
  quality_consistency: "Maintain consistent progressive standards across all agents and thinking levels"
  recovery_protocols: "Comprehensive error handling with mcp research and multiple errors fail and cognitive recovery procedures"
```

## 🧠 META-COGNITIVE & CONSTITUTIONAL FRAMEWORK

```yaml
COGNITIVE_ARCHITECTURE:
  meta_cognition: "Think about the thinking process, identify biases, apply constitutional analysis"
  multi_perspective_analysis:
    - "user_perspective: Understanding user intent and constraints"
    - "developer_perspective: Technical implementation and architecture considerations"
    - "business_perspective: Cost, timeline, and stakeholder impact analysis"
    - "security_perspective: Risk assessment and compliance requirements"
    - "quality_perspective: Standards enforcement and continuous improvement"

VIBECODER_ENGINEERING_PRINCIPLES:
  kiss_principle:
    definition: "Keep It Simple, Stupid - Simplicity is key"
    core_rules: [
      "Choose simplest solution that meets requirements",
      "Prefer readable code over clever optimizations",
      "Reduce cognitive load",
      "Does this solve the core problem without unnecessary complexity?",
      "Use clear, descriptive naming and avoid Over-Engineering",
    ]

  yagni_principle:
    definition: "You Aren't Gonna Need It - Don't implement until needed"
    core_rules: [
      "Build only what current requirements specify",
      "Resist 'just in case' features",
      "Refactor when requirements emerge",
      "Focus on current user stories",
      "Remove unused code immediately",
    ]

  chain_of_thought:
    definition: "Explicit step-by-step reasoning for accuracy"
    core_rules: [
      "Break problems into sequential steps",
      "Verbalize reasoning process",
      "Show intermediate decisions",
      "Question assumptions",
      "Validate against requirements",
      "Each step follows logically from previous steps",
      "Final solution traced back to requirements",
    ]

CONSTITUTIONAL_PRINCIPLES:
  principle_based_design: "Align with software engineering excellence"
  constraint_satisfaction: "Balance competing requirements optimally"
  risk_assessment: "Multi-dimensional risk analysis"
  quality_gates: "Define success criteria and validation checkpoints"
  continuous_improvement: "Iterate based on feedback and metrics"
  relentless_persistence: "Continue until absolute completion"
  complete_execution: "Execute entire workflow without interruption"
  right_tool_selection: "Understand full context before implementation"
```

## 🔧 MCP COORDINATION & TOOL SELECTION

### **🎯 MCP Capabilities Matrix**

| MCP Server                 | Core Functions                                                       | Primary Use Cases                                                     | Mandatory/Contextual                  |
| -------------------------- | -------------------------------------------------------------------- | --------------------------------------------------------------------- | ------------------------------------- |
| **🧠 sequential-thinking** | Complex problem decomposition, systematic analysis, branching logic  | Breaking down complex problems, decision making, systematic reasoning | **MANDATORY FIRST STEP**              |
| **📋 archon**              | Task management, project organization, knowledge base, RAG queries   | Project coordination, task tracking, knowledge management             | **MANDATORY for task management**     |
| **🔍 serena**              | Codebase search, semantic analysis, symbol navigation, refactoring   | Code understanding, symbol analysis, codebase navigation              | **MANDATORY for codebase operations** |
| **💻 desktop-commander**   | File operations, system management, data analysis, process execution | File system operations, data analysis, scaffolding, system commands   | **Contextual - file operations**      |
| **📚 context7**            | Documentation research, framework lookup, library resolution         | Framework research, best practices validation, official documentation | **Contextual - research phase**       |
| **🌐 tavily**              | Real-time web search, current information, trend analysis            | Current trends, technology updates, real-time information             | **Contextual - current info needed**  |
| **🎨 shadcn-ui**           | UI component management, design system integration                   | UI development, component library management                          | **Contextual - UI work**              |

### **🔄 Mandatory MCP Workflow**

```yaml
MANDATORY_SEQUENCE:
  step_1: "sequential-thinking (ALWAYS FIRST - problem decomposition)"
  step_2: "archon (task management and knowledge base)"
  step_3: "serena (codebase analysis - NEVER use native search)"

CONSTITUTIONAL_RULE: "Never skip mandatory MCPs or use native alternatives"
QUALITY_GATE: "Each mandatory MCP must complete successfully before proceeding"
```

### **🎯 MCP Selection Decision Tree**

```yaml
DECISION_MATRIX:
  problem_analysis:
    complexity_level_1-3: "sequential-thinking only"
    complexity_level_4-6: "sequential-thinking → archon → serena"
    complexity_level_7-10: "sequential-thinking → archon → context7 → serena"

  task_type:
    research_heavy: "sequential-thinking → archon → context7 → tavily"
    implementation_focused: "sequential-thinking → archon → serena → desktop-commander"
    ui_development: "sequential-thinking → archon → serena → shadcn-ui → desktop-commander"
    data_analysis: "sequential-thinking → desktop-commander (with Python/analysis tools)"

  information_needs:
    framework_documentation: "context7 (resolve-library-id → get-library-docs)"
    current_trends: "tavily (search → searchContext → extract)"
    codebase_understanding: "serena (get_symbols_overview → find_symbol → find_referencing_symbols)"
    project_context: "archon (perform_rag_query → get_project → list_tasks)"
```

### **🔗 MCP Coordination Workflows**

#### **🔬 Research-Driven Development Chain**

```yaml
sequence: "sequential-thinking → archon → context7 → tavily → serena → desktop-commander"
purpose: "Evidence-based implementation with multi-source validation"
use_cases: ["New framework integration", "Complex feature development", "Architecture decisions"]
quality_gate: "≥95% research confidence before implementation"
```

#### **⚡ Rapid Implementation Chain**

```yaml
sequence: "sequential-thinking → archon → serena → desktop-commander"
purpose: "Fast implementation of well-understood requirements"
use_cases: ["Bug fixes", "Simple features", "Code refactoring"]
quality_gate: "Clear requirements and existing patterns identified"
```

#### **🎨 UI Development Chain**

```yaml
sequence: "sequential-thinking → archon → serena → shadcn-ui → desktop-commander"
purpose: "Healthcare-optimized UI development with accessibility"
use_cases: ["Component creation", "UI refactoring", "Design system updates"]
quality_gate: "WCAG 2.1 AA+ compliance and component integration verified"
```

#### **📊 Data Analysis Chain**

```yaml
sequence: "sequential-thinking → desktop-commander (Python REPL) → archon (documentation)"
purpose: "File-based data analysis and processing"
use_cases: ["CSV analysis", "Log processing", "Performance metrics"]
quality_gate: "Analysis results validated and documented"
```

### **🚨 Error Recovery & Troubleshooting Protocols**

```yaml
RECOVERY_PROTOCOLS:
  mcp_failure:
    step_1: "Document the specific error and context"
    step_2: "Try alternative MCP if available (e.g., tavily if context7 fails)"
    step_3: "Use sequential-thinking to analyze the problem"
    step_4: "Consult archon knowledge base for similar issues"
    step_5: "If stuck >3 attempts, initiate new research cycle"

  infinite_loops:
    detection: "Same MCP called >3 times with similar parameters"
    action: "Stop current approach, use sequential-thinking to reassess"
    alternative: "Switch to different MCP or break down problem further"

  context_loss:
    prevention: "Always maintain context across MCP transitions"
    recovery: "Use archon to retrieve project context and serena for codebase state"
    validation: "Verify context completeness before proceeding"
```

### **✅ MCP Best Practices**

#### **🧠 sequential-thinking**

```yaml
DO:
  - Always use as first step for complex problems (complexity ≥4)
  - Break down problems into logical, sequential steps
  - Use branching and revision when exploring alternatives
  - Generate and validate hypotheses systematically

DON'T:
  - Skip for complex problems to save time
  - Use for simple, well-understood tasks
  - Forget to validate reasoning against requirements
```

#### **📋 archon**

```yaml
DO:
  - Start with archon for all task management
  - Use perform_rag_query to leverage knowledge base
  - Update task status throughout development
  - Document decisions and learnings

DON'T:
  - Skip archon task management workflow
  - Forget to update task progress
  - Ignore existing project knowledge
```

#### **🔍 serena**

```yaml
DO:
  - Use for ALL codebase operations (mandatory)
  - Start with get_symbols_overview for new files
  - Use find_symbol for specific code elements
  - Leverage semantic search capabilities

DON'T:
  - Use native codebase-retrieval tool
  - Skip symbol analysis for complex changes
  - Ignore referencing symbols when refactoring
```

#### **💻 desktop-commander**

```yaml
DO:
  - Use for file operations and system management
  - Leverage Python REPL for data analysis
  - Use interactive processes for complex operations
  - Chunk file operations (25-30 lines max)

DON'T:
  - Use for codebase search (use serena instead)
  - Write large files in single operations
  - Ignore file operation errors
```

#### **📚 context7**

```yaml
DO:
  - Resolve library IDs before getting documentation
  - Focus searches with specific topics
  - Cross-reference with tavily for current information
  - Validate documentation currency

DON'T:
  - Skip library ID resolution
  - Use for general web search (use tavily)
  - Assume documentation is current without validation
```

#### **🌐 tavily**

```yaml
DO:
  - Use for current trends and real-time information
  - Leverage different search types (basic, context, QNA)
  - Extract content from multiple sources
  - Cross-validate information

DON'T:
  - Use for framework documentation (use context7)
  - Rely on single source for critical decisions
  - Skip information validation
```

#### **🎨 shadcn-ui**

```yaml
DO:
  - Check component availability before custom development
  - Use demo code as implementation reference
  - Leverage block components for complex layouts
  - Maintain design system consistency

DON'T:
  - Reinvent existing components
  - Ignore accessibility features
  - Skip component metadata review
```

### **🔄 Integration with Workflow Phases**

```yaml
PHASE_INTEGRATION:
  research_decomposition:
    primary: "sequential-thinking (mandatory)"
    support: "archon (knowledge base), context7 (documentation)"

  planning_task_list:
    primary: "archon (mandatory task management)"
    support: "serena (codebase analysis), sequential-thinking (planning)"

  implementation:
    primary: "serena (mandatory codebase), desktop-commander (file ops)"
    support: "shadcn-ui (UI components), archon (progress tracking)"

  testing_validation:
    primary: "desktop-commander (test execution)"
    support: "serena (code analysis), archon (documentation)"

  quality_checks:
    primary: "desktop-commander (linting, type checking)"
    support: "serena (code quality analysis)"

  documentation:
    primary: "desktop-commander (file creation)"
    support: "archon (knowledge management), serena (code references)"
```

## Mandatory Development Steps

### 1. **Research & Decomposition** 🧠

**🔧 MCP Integration**: Follow the **Mandatory MCP Workflow** (sequential-thinking → archon → serena)

- **MANDATORY FIRST STEP**: Always begin with the `sequential-thinking` tool to analyze and understand the feature requirements
- **MANDATORY SECOND STEP**: Use `archon` to check existing project context and create/update tasks
- **MANDATORY THIRD STEP**: Use `serena` (NEVER native codebase-retrieval) to understand current codebase state
- Use systematic thinking to break down the feature into smaller components
- Identify potential complexities, dependencies, and constraints
- **MCP Selection**: For complexity ≥7, activate Research-Driven Development Chain (add context7 → tavily)
- If tried more than 3 times to fix something and still stuck, start a new research cycle using MCP Error Recovery Protocol

**🔬 Ativação Automática do @apex-researcher**:

- Implementações técnicas complexas
- Integração de novos frameworks/bibliotecas
- Requisitos de segurança/compliance
- Necessidades de otimização de performance
- Decisões arquiteturais
- Regulamentações específicas de saúde (HIPAA, LGPD)

**Comando de Ativação**:

```bash
@apex-researcher "pesquisar [tecnologia/padrão/regulamentação]"
```

- Follow the Research Execution Framework below

```yaml
RESEARCH_EXECUTION_FRAMEWORK:
  vibecoder_integration: "Think & Analyze - ALWAYS before any action - NO EXCEPTIONS"
  primary_tool: "sequential-thinking + native think tool (MANDATORY FIRST STEP)"
  purpose: "Comprehensive requirement analysis with context understanding and complexity assessment"
  intelligence: "Dynamic complexity scoring (L1-L10) with automatic cognitive and agent routing"
  thinking_activation: "Auto-scaled based on complexity detection and domain triggers"
  routing: "Intelligent MCP selection based on complexity, domain, and requirements analysis"
  agent_coordination: "Sequential thinking primary, potential apex_researcher_agent for complex analysis"
  process:
    - "Understand requirements completely with constitutional analysis"
    - "Identify constraints and dependencies with multi-perspective evaluation"
    - "Assess complexity level (1-10) with domain-specific triggers"
    - "Define strategic approach with agent coordination planning"
    - "Break down into manageable components with quality gate definition"
  deliverables: "Complete requirements with execution plan, cognitive activation, and agent assignments"
  purpose: "Knowledge acquisition and validation for informed implementation with multi-source validation"
  intelligence: "Multi-source research orchestration with authority validation and cross-referencing"
  routing: "Context7 → Tavily → Archon → Exa chain for complexity, with sequential thinking integration"
  quality_gate: "100% requirement clarity with multi-source validation and constitutional compliance"
```

### 3. **Planning & Task List** 📋

**🔧 MCP Integration**: Primary `archon` for task management, support from `serena` and `sequential-thinking`

- **MANDATORY**: Use `archon` for all task management (create_task, update_task, list_tasks)
- Create detailed task list using archon's task management system
- Break down complex features into manageable subtasks: atomic tasks on archon, then subtasks on native todos
- **MCP Coordination**: Use `serena` to analyze codebase complexity and dependencies
- Mark tasks as in_progress when starting, completed when finished
- **Follow the Project Context Guide:** [`docs/project.md`](../docs/project.md)

**🎯 Ativação de Agentes por Contexto**:

```bash
# Planejamento Técnico
@apex-dev "planejar implementação de [feature]"

# Planejamento de UI/UX
@apex-ui-ux-designer "projetar interface para [funcionalidade]"

# Planejamento Arquitetural
@architect "definir arquitetura para [sistema]"

# Planejamento Colaborativo
@apex-dev,architect "planejar sistema de [funcionalidade complexa]"
```

- Follow the Planning & Design Framework below

```yaml
phase_3_planning_design:
  vibecoder_integration: "Context Engineering & Planning with ONE-SHOT template methodology"
  contextual_loading: "UI/UX agent loaded automatically when design tasks detected"
  purpose: "Solution architecture and strategic implementation planning with systematic design approach"
  intelligence: "Risk assessment with comprehensive mitigation strategies and architectural validation"
  routing: "Sequential Thinking integration with architectural pattern analysis and validation"
  one_shot_template:
    role: "[Specific: Frontend Developer | Backend Engineer | Full-Stack | UI/UX Designer]"
    context: "#workspace + #codebase search with serena mcp + [archon knowledge base + relevant files]"
    task: "[Specific, measurable, actionable requirement]"
    constraints: "[Technical limitations, performance requirements]"
    output: "[Code | Documentation | Architecture | Analysis | Design]"
    success_criteria: "[Measurable outcomes, quality thresholds]"
  task_planning:
    structure:
      - "Break down into atomic executable tasks with agent assignment"
      - "Assign optimal tools and agents for each task with specialization matching"
      - "Define validation checkpoints with quality gates"
      - "Create dependency mapping with agent coordination requirements"
      - "Set measurable success criteria with progressive quality standards"
  deliverables: "Detailed execution plan with quality gates, agent coordination, and architectural specifications"
  quality_gate: "Architecture review ≥9.5/10 with scalability validation and security compliance"
```

### 3. **TDD Implementation (Red-Green-Refactor)**

- **MANDATORY**: Follow the TDD process as described in `docs/ttd-flow.md`
- **TDD Cycle for each feature component:**
  1. **RED**: Write failing test first (describe expected behavior)
  2. **GREEN**: Write minimal code to pass the test
  3. **REFACTOR**: Improve code while keeping tests green
  4. **REPEAT**: Continue cycle for next requirement

- **Test Priority (from ttd-flow.md):**
  - 🔥 **CRITICAL**: Business logic, AI agents, APIs, financial operations
  - ⚡ **IMPORTANT**: Complex hooks, utilities, data validation, integrations
  - ✅ **USEFUL**: UI components with logic, helpers

- **Implementation Guidelines:**
  - **🔧 MCP Integration**: Use Implementation Chain (serena → desktop-commander, +shadcn-ui for UI)
  - **MANDATORY**: Execute following ALL guidelines from `/.ruler` and `/docs` directories
  - **ALWAYS**: Use `serena` for codebase analysis (get_symbols_overview → find_symbol → find_referencing_symbols)
  - **File Operations**: Use `desktop-commander` for all file operations (chunked 25-30 lines max)
  - **UI Components**: Use `shadcn-ui` to check component availability before custom development
  - Follow established code patterns, naming conventions, and project standards
  - **MCP Error Recovery**: If stuck >3 attempts, use sequential-thinking to reassess and try alternative MCP
  - Use Native `think` tool every 5 steps to ensure alignment with requirements and quality
  - Implement comprehensive error handling following project patterns
  - **Test Categories Required**: Success cases, error cases, edge cases, business logic
  - **Documentation First**: Check existing patterns with `serena` before creating new ones

### 4. **Test Execution & Validation** ✅

**🔧 MCP Integration**: Primary `desktop-commander` for test execution, support from `serena` and `archon`

- **MCP Test Execution**: Use `desktop-commander` to run all test commands in interactive processes
- Run `pnpm format:check` to check for formatting issues
- Run `npx oxlint apps packages --fix` to fix linter issues (Note: dprint formatting requires platform-specific dependency)
- Run `pnpm format && pnpm lint:fix && pnpm type-check` to format code
- Run `pnpm test` and all task tests to execute all unit tests
- **Correction Loop with MCP**: If tests fail:
  - Use `serena` to analyze failing code and understand dependencies
  - Fix the issues following TDD principles
  - Use `desktop-commander` to run tests again
  - **MCP Error Recovery**: If stuck >3 attempts, use sequential-thinking to reassess approach
  - Repeat until ALL tests pass
- **Coverage Requirements** (from ttd-flow.md):
  - Critical business logic: 100%
  - AI agents/services: 90%+
  - Complex hooks: 85%+
  - Utilities/validators: 80%+
- **MCP Documentation**: Use `archon` to document test results and coverage metrics
- Only proceed when all unit tests are green and coverage meets requirements
- Use Tasks tests to complement unit tests
- Run `run tasks:test` to execute all task tests

### 5. **Code Quality Check** 🔍

**🔧 MCP Integration**: Primary `desktop-commander` for quality checks, support from `serena` for code analysis

- **MCP Quality Execution**: Use `desktop-commander` to run all quality check commands
- Run `pnpx next lint` to check for linting issues
- Run `pnpx tsc --noEmit` to verify TypeScript compilation
- **MCP Code Analysis**: Use `serena` to analyze code quality and identify potential issues
- Fix any errors or warnings before proceeding
- **Correction Loop with MCP**: If issues are found:
  - Use `serena` to understand code structure and dependencies before fixing
  - Fix the reported issues using `desktop-commander` for file operations
  - **MCP Error Recovery**: If stuck >3 attempts, use sequential-thinking to reassess
  - Return to **Step 4** (Unit Testing) and repeat the entire cycle
  - Continue until ALL quality checks pass
  - Only proceed when linting and type checks are clean

```yaml
validation:
  vibecoder_integration: "Quality Validation & Testing with constitutional enforcement gates"
  architecture_analysis: "Always check architecture docs for best practices validation"
  technology_excellence: "Framework best practices compliance and performance optimization"
  qa_mandatory:
    post_modification_checks:
      - "Syntax errors verification with zero tolerance policy"
      - "Duplicates/orphans detection with cleanup protocols"
      - "Feature validation against requirements with completeness verification"
      - "Requirements compliance with constitutional principles validation"
      - "Security vulnerabilities assessment with compliance verification"
      - "Test coverage ≥90% with comprehensive testing protocols"
  verification_rule: "Never assume changes complete without explicit verification"
```

### 6. **Memory Documentation Protocol** 📝

**🔧 MCP Integration**: Primary `desktop-commander` for file operations, `archon` for knowledge management

- **MANDATORY**: Follow the Proactive Update Protocol from `docs/memory.md`:
  - **MCP Documentation**: Use `desktop-commander` to create documentation files (chunked operations)
  - **Knowledge Management**: Use `archon` to create/update project documents and maintain knowledge base
  - Create `mistakes/[error-category].md` if any mistake was made and corrected
  - Create `features/[feature-name].md` for new features or modified behavior
  - **TDD Documentation**: Include test coverage metrics, test patterns used, and any TDD-specific decisions
  - **MCP Usage Documentation**: Document which MCPs were used and why for future reference
  - Update `docs\project.md` if new project standards were established
  - Update `.ruler\code-preferences.md` if coding preferences were clarified
- **MCP File Operations**: Use `desktop-commander` to update relevant README files if needed
- **Test Documentation**: Document any new test patterns, mocks, or testing utilities created during TDD process
- **Archon Integration**: Store learnings and decisions in archon knowledge base for future retrieval

### 10. **Documentation Folder Updates** 📚

**🔧 MCP Integration**: Primary `desktop-commander` for file operations, `serena` for codebase analysis, `archon` for documentation management

- **MCP-Enhanced Evaluation**: Use `serena` to analyze code changes and identify documentation impact
- **Evaluate and Suggest**: Assess if the following documentation folders need updates based on the implemented feature:
  - Application flows (`docs/app-flows/`) - if user flows were modified
  - API documentation (`docs/apis/`) - if endpoints were created/modified
  - Database schemas (`docs/database-schema/`) - if database structure changed
  - Any other relevant documentation folders
- **MCP Documentation Operations**: Use `desktop-commander` to create/update documentation files
- **Knowledge Base Integration**: Use `archon` to store documentation updates in project knowledge base
- **Suggest to User**: Recommend specific updates needed and ask user to review/update the identified documentation folders

### 11. **Out-of-Scope Documentation**

- **🔄 Ativação do @documentation**: Para documentação não coberta por protocolos existentes

```bash
@documentation "criar documentação para [conceito/padrão/integração]"
```

- Use o agente de documentação para criar documentação abrangente para:
  - Novos conceitos ou padrões introduzidos
  - Decisões arquiteturais complexas
  - Guias de integração ou tutoriais
  - Qualquer documentação fora dos protocolos padrão de memória e pastas

## 📚 **Referências dos Agentes**

- **🤖 Sistema de Coordenação**: [`.ruler/agents/AGENT.md`](../.ruler/agents/AGENT.md)
- **💻 @apex-dev**: [`.ruler/agents/apex-dev.md`](../.ruler/agents/apex-dev.md)
- **🔬 @apex-researcher**: [`.ruler/agents/apex-researcher.md`](../.ruler/agents/apex-researcher.md)
- **🎨 @apex-ui-ux-designer**: [`.ruler/agents/apex-ui-ux-designer.md`](../.ruler/agents/apex-ui-ux-designer.md)
- **🏗️ @architect**: [`.ruler/agents/architect.md`](../.ruler/agents/architect.md)
- **🔒 @audit**: [`.ruler/agents/audit.md`](../.ruler/agents/audit.md)
- **📋 @prd**: [`.ruler/agents/prd.md`](../.ruler/agents/prd.md)
- **🔧 @refactor**: [`.ruler/agents/refactor.md`](../.ruler/agents/refactor.md)
- **📢 @briefing**: [`.ruler/agents/briefing.md`](../.ruler/agents/briefing.md)
- **📚 @documentation**: [`.ruler/agents/documentation.md`](../.ruler/agents/documentation.md)
- **⚖️ @rules**: [`.ruler/agents/rules.md`](../.ruler/agents/rules.md)

## Important Notes

- **🚫 Never skip steps** unless explicitly told by the user
- **🔧 Always follow MCP Mandatory Sequence**: sequential-thinking → archon → serena (NEVER native codebase-retrieval)
- **📖 Always consult `/docs`** when uncertain, using `desktop-commander` for file operations
- **✅ Complete each step** before moving to the next, using appropriate MCPs for each phase
- **🔄 Iterate** until all quality checks pass, using MCP Error Recovery Protocol if stuck
- **📝 Document everything** for future reference using `archon` knowledge base and `desktop-commander` file operations
- **🎯 MCP Selection**: Use the MCP Capabilities Matrix and Decision Tree for optimal tool selection
- **🚨 Error Recovery**: If any MCP fails >3 times, use sequential-thinking to reassess and try alternatives
