<!-- Powered by BMAD™ Core -->

# Create AI Frontend Prompt Task

## Purpose

To generate a masterful, comprehensive, and optimized prompt that can be used with any AI-driven
frontend development tool (e.g., Vercel v0, Lovable.ai, or similar) to scaffold or generate
significant portions of a frontend application.

## Inputs

- Completed UI/UX Specification (`front-end-spec.md`)
- Completed Frontend Architecture Document (`front-end-architecture`) or a full stack combined
  architecture such as `architecture.md`
- Main System Architecture Document (`architecture` - for API contracts and tech stack to give
  further context)

## Key Activities & Instructions

### 1. Core Prompting Principles

Before generating the prompt, you must understand these core principles for interacting with a
generative AI for code.

- **Be Explicit and Detailed**: The AI cannot read your mind. Provide as much detail and context as
  possible. Vague requests lead to generic or incorrect outputs.
- **Iterate, Don't Expect Perfection**: Generating an entire complex application in one go is rare.
  The most effective method is to prompt for one component or one section at a time, then build upon
  the results.
- **Provide Context First**: Always start by providing the AI with the necessary context, such as
  the tech stack, existing code snippets, and overall project goals.
- **Mobile-First Approach**: Frame all UI generation requests with a mobile-first design mindset.
  Describe the mobile layout first, then provide separate instructions for how it should adapt for
  tablet and desktop.

### 2. The Structured Prompting Framework

To ensure the highest quality output, you MUST structure every prompt using the following four-part
framework.

1. **High-Level Goal**: Start with a clear, concise summary of the overall objective. This orients
   the AI on the primary task.
   - _Example: "Create a responsive user registration form with client-side validation and API
     integration."_
2. **Detailed, Step-by-Step Instructions**: Provide a granular, numbered list of actions the AI
   should take. Break down complex tasks into smaller, sequential steps. This is the most critical
   part of the prompt.
   - _Example: "1. Create a new file named `RegistrationForm.js`. 2. Use React hooks for state
     management. 3. Add styled input fields for 'Name', 'Email', and 'Password'. 4. For the email
     field, ensure it is a valid email format. 5. On submission, call the API endpoint defined
     below."_
3. **Code Examples, Data Structures & Constraints**: Include any relevant snippets of existing code,
   data structures, or API contracts. This gives the AI concrete examples to work with. Crucially,
   you must also state what _not_ to do.
   - _Example: "Use this API endpoint: `POST /api/register`. The expected JSON payload is
     `{ "name": "string", "email": "string", "password": "string" }`. Do NOT include a 'confirm
     password' field. Use Tailwind CSS for all styling."_
4. **Define a Strict Scope**: Explicitly define the boundaries of the task. Tell the AI which files
   it can modify and, more importantly, which files to leave untouched to prevent unintended changes
   across the codebase.
   - _Example: "You should only create the `RegistrationForm.js` component and add it to the
     `pages/register.js` file. Do NOT alter the `Navbar.js` component or any other existing page or
     component."_

### 3. Assembling the Master Prompt

You will now synthesize the inputs and the above principles into a final, comprehensive prompt.

1. **Gather Foundational Context**:
   - Start the prompt with a preamble describing the overall project purpose, the full tech stack
     (e.g., Next.js, TypeScript, Tailwind CSS), and the primary UI component library being used.
2. **Describe the Visuals**:
   - If the user has design files (Figma, etc.), instruct them to provide links or screenshots.
   - If not, describe the visual style: color palette, typography, spacing, and overall aesthetic
     (e.g., "minimalist", "corporate", "playful").
3. **Build the Prompt using the Structured Framework**:
   - Follow the four-part framework from Section 2 to build out the core request, whether it's for a
     single component or a full page.
4. **Present and Refine**:
   - Output the complete, generated prompt in a clear, copy-pasteable format (e.g., a large code
     block).
   - Explain the structure of the prompt and why certain information was included, referencing the
     principles above.
   - <important_note>Conclude by reminding the user that all AI-generated code will require careful
     human review, testing, and refinement to be considered production-ready.</important_note>
