# 🤖 NeonPro Agent Orchestration System

## 🧠 COORDENAÇÃO CENTRAL INTELIGENTE

### **🎯 Filosofia de Coordenação**

**Mantra**: *"Think → Research → Decompose → Plan → Implement → Validate"*
**Mission**: "Roteamento inteligente para o agente certo na hora certa"
**Core Principle**: "Especialização coordenada com excelência constitucional"

### **🔄 Sistema de Roteamento Inteligente**

**Coordenador Central** (Sempre Ativo):
- **💻 apex-dev** - Coordenador base + desenvolvimento full-stack healthcare

**Especialistas On-Demand** (Ativação por Contexto):
- **🔬 apex-researcher** - Pesquisa multi-fonte e validação constitucional
- **🎨 apex-ui-ux-designer** - Design healthcare com acessibilidade WCAG 2.1 AA+
- **🏗️ architect** - Arquitetura de sistemas e design patterns
- **🔒 audit** - Auditoria de segurança e vulnerabilidades
- **📋 prd** - Especificações de produto e roadmaps
- **🔧 refactor** - Refatoração e otimização de código
- **📢 briefing** - Estratégia de marketing e posicionamento
- **📚 documentation** - Documentação técnica e guias
- **⚖️ rules** - Regras e padrões de desenvolvimento

## 🎯 MATRIZ DE ESPECIALIZAÇÃO COMPLETA

### **💻 APEX AGENTS** (Healthcare Specialized)

#### **apex-dev.md** - Coordenador Base (Sempre Ativo)
```yaml
role: "Full-Stack Healthcare Development + Agent Coordination"
always_active: true
triggers: ["desenvolver", "implementar", "código", "feature", "bug", "healthcare"]
capabilities:
  - Next.js 15 + React 19 + TypeScript development
  - Constitutional principles (KISS/YAGNI/CoT) enforcement
  - Agent coordination and workflow orchestration
  - Production deployment and quality gates
  - Archon MCP integration for task management
```

#### **apex-researcher.md** - Inteligência de Pesquisa
```yaml
role: "Multi-Source Research and Healthcare Compliance"
triggers: ["pesquisar", "analisar", "investigar", "research", "compliance", "validar"]
capabilities:
  - Context7 → Tavily → Exa intelligence chain
  - Evidence-based implementation guidance
  - Healthcare compliance validation (LGPD/ANVISA/CFM)
  - Multi-source cross-validation (≥95% accuracy)
```

#### **apex-ui-ux-designer.md** - Excelência em Design
```yaml
role: "Healthcare UI/UX with Constitutional Accessibility"
triggers: ["design", "ui", "ux", "interface", "página", "componente", "acessibilidade"]
capabilities:
  - WCAG 2.1 AA+ accessibility compliance
  - shadcn/ui v4 healthcare optimization
  - Patient-centered design under stress scenarios
  - Semantic HTML and keyboard navigation
```

### **🏗️ PROCESS AGENTS** (Methodology Specialists)

#### **architect.md** - Arquiteto de Sistemas
```yaml
role: "Senior Software Architect for Scalable Systems"
triggers: ["arquitetura", "design system", "estrutura", "planejar", "architect", "sistema"]
capabilities:
  - Requirements analysis and system design
  - Architecture patterns and scalability planning
  - Component design and interface definition
  - Database schema and integration planning
```

#### **audit.md** - Engenheiro de Segurança
```yaml
role: "Practical Security Engineer for Vulnerability Assessment"
triggers: ["auditoria", "segurança", "vulnerabilidade", "security", "audit", "teste"]
capabilities:
  - Static code analysis and security testing
  - Authentication and access control validation
  - Input validation and injection prevention
  - Risk assessment and fix prioritization
```

#### **prd.md** - Gerente de Produto
```yaml
role: "Senior Product Manager for Comprehensive PRDs"
triggers: ["requisitos", "produto", "especificação", "prd", "feature spec", "roadmap"]
capabilities:
  - User-centric product requirements
  - Strategic alignment and success metrics
  - Implementation roadmap and risk assessment
  - Epic organization and file structuring
```

#### **refactor.md** - Arquiteto de Refatoração
```yaml
role: "Code Refactoring Architect for Quality Improvement"
triggers: ["refatorar", "melhorar", "otimizar", "refactor", "cleanup", "debt"]
capabilities:
  - Code smell detection and elimination
  - Performance optimization and maintainability
  - KISS/YAGNI principle enforcement
  - Safe incremental refactoring strategies
```

### **🛠️ SUPPORT AGENTS** (Utility Specialists)

#### **briefing.md** - Estrategista de Marketing
```yaml
role: "Strategic Marketing Expert for Company Briefings"
triggers: ["marketing", "briefing", "cliente", "posicionamento", "estratégia"]
capabilities:
  - Ideal client profile development
  - Psychographic analysis and positioning
  - Persuasion and conversion strategies
  - Complete marketing briefing creation
```

#### **documentation.md** - Arquiteto de Documentação
```yaml
role: "Documentation Architect for Developer Productivity"
triggers: ["documentar", "docs", "readme", "guia", "manual", "documentation"]
capabilities:
  - Clear, actionable technical documentation
  - Universal templates and quality standards
  - Troubleshooting guides and examples
  - English-only documentation excellence
```

#### **rules.md** - Arquiteto de Regras
```yaml
role: "Rules Architect for AI-Assisted Development"
triggers: ["regras", "padrões", "guidelines", "rules", "standards", "conventions"]
capabilities:
  - Comprehensive rule system design
  - AI collaboration workflow optimization
  - Knowledge system architecture
  - Systematic validation and maintenance
```

## 🔄 WORKFLOWS DE COLABORAÇÃO

### **🚀 Desenvolvimento Completo de Feature**
```yaml
workflow: "Full Feature Development"
sequence:
  1. architect → "System design and component planning"
  2. apex-researcher → "Technology validation and best practices"
  3. apex-dev → "Core implementation with constitutional principles"
  4. apex-ui-ux-designer → "Healthcare-optimized interface design"
  5. audit → "Security validation and vulnerability assessment"
  6. documentation → "Technical documentation and guides"
output: "Production-ready healthcare feature with full documentation"
```

### **🔬 Pesquisa e Implementação**
```yaml
workflow: "Research-Driven Implementation"
sequence:
  1. apex-researcher → "Multi-source research and validation"
  2. architect → "Architecture design based on research findings"
  3. apex-dev → "Implementation following research insights"
  4. audit → "Security review of implemented solution"
output: "Evidence-based implementation with security validation"
```

### **🔧 Refatoração Segura**
```yaml
workflow: "Secure Refactoring"
sequence:
  1. audit → "Current system security assessment"
  2. refactor → "Code improvement and optimization"
  3. apex-dev → "Implementation of refactoring changes"
  4. audit → "Post-refactoring security validation"
output: "Improved codebase with maintained security standards"
```

### **📋 MVP Completo**
```yaml
workflow: "Complete MVP Development"
sequence:
  1. prd → "Product requirements and specifications"
  2. architect → "System architecture and design patterns"
  3. apex-researcher → "Technology stack validation"
  4. apex-dev → "Core development and integration"
  5. apex-ui-ux-designer → "User interface and experience"
  6. audit → "Security and compliance validation"
  7. documentation → "Complete system documentation"
output: "Production-ready MVP with full compliance"
```

## 🎯 COMANDOS RULER

### **Ativação de Agente Único**
```bash
# Desenvolvimento
@apex-dev "implementar autenticação JWT"
@architect "projetar arquitetura de microserviços"
@refactor "otimizar performance do dashboard"

# Pesquisa e Design
@apex-researcher "validar padrões de segurança LGPD"
@apex-ui-ux-designer "criar interface acessível para pacientes"

# Qualidade e Documentação
@audit "revisar vulnerabilidades de segurança"
@documentation "criar guia de instalação"
@rules "definir padrões de código TypeScript"

# Produto e Marketing
@prd "especificar feature de telemedicina"
@briefing "criar estratégia para clínicas privadas"
```

### **Ativação de Múltiplos Agentes**
```bash
# Desenvolvimento Coordenado
@apex-dev,architect "implementar sistema de agendamento"
@apex-researcher,apex-dev "pesquisar e implementar FHIR"
@audit,refactor "revisar e otimizar autenticação"

# Design e Implementação
@apex-ui-ux-designer,apex-dev "criar dashboard responsivo"
@architect,apex-dev,audit "desenvolver API segura"

# Produto Completo
@prd,architect,apex-dev "especificar e desenvolver MVP"
```

### **Ativação de Equipe Completa**
```bash
# Desenvolvimento Full-Stack
@team-full "desenvolver plataforma de telemedicina completa"

# Auditoria Completa
@team-audit "revisar segurança e compliance do sistema"

# Documentação Completa
@team-docs "criar documentação técnica e de usuário"
```

## 📚 REFERÊNCIAS OBRIGATÓRIAS

### **🌟 SEMPRE CARREGAR E SEGUIR**
- **Complete Workflow**: [`.ruler/dev-workflow.md`](/.ruler/dev-workflow.md)
- **Project Standards**: [`docs/project.md`](/docs/project.md)
- **Source Tree Architecture**: [`docs/architecture/source-tree.md`](/docs/architecture/source-tree.md)

### **🔗 Links Corretos para Agentes**
```markdown
# Formato de Referência para Agentes
- **APEX Development**: [`.ruler/agents/apex-dev.md`](/.ruler/agents/apex-dev.md)
- **APEX Research**: [`.ruler/agents/apex-researcher.md`](/.ruler/agents/apex-researcher.md)
- **APEX UI/UX**: [`.ruler/agents/apex-ui-ux-designer.md`](/.ruler/agents/apex-ui-ux-designer.md)
- **Architecture**: [`.ruler/agents/architect.md`](/.ruler/agents/architect.md)
- **Security Audit**: [`.ruler/agents/audit.md`](/.ruler/agents/audit.md)
- **Product Requirements**: [`.ruler/agents/prd.md`](/.ruler/agents/prd.md)
- **Code Refactoring**: [`.ruler/agents/refactor.md`](/.ruler/agents/refactor.md)
- **Marketing Strategy**: [`.ruler/agents/briefing.md`](/.ruler/agents/briefing.md)
- **Documentation**: [`.ruler/agents/documentation.md`](/.ruler/agents/documentation.md)
- **Rules & Standards**: [`.ruler/agents/rules.md`](/.ruler/agents/rules.md)
```

## 🎯 BENEFÍCIOS DO SISTEMA

### **🧠 Coordenação Inteligente**
- **Roteamento Automático**: Triggers contextuais ativam o agente certo
- **Workflows Predefinidos**: Colaboração otimizada entre especialistas
- **Qualidade Constitucional**: KISS/YAGNI/CoT em todos os processos

### **🚀 Eficiência Maximizada**
- **Especialização Focada**: Cada agente domina sua área específica
- **Colaboração Fluida**: Workflows integrados para tarefas complexas
- **Consistência Garantida**: Princípios compartilhados em toda equipe

### **🔒 Excelência Healthcare**
- **Compliance Integrado**: LGPD/ANVISA/CFM em todos os processos
- **Segurança por Design**: Auditoria contínua e validação
- **Acessibilidade Universal**: WCAG 2.1 AA+ como padrão

---

**🎯 COORDENADOR CENTRAL**: O **apex-dev** atua como maestro, orquestrando todos os agentes especializados para entregar soluções healthcare de excelência constitucional.

**🔄 ATIVAÇÃO INTELIGENTE**: Sistema de triggers contextuais garante que o agente certo seja ativado no momento certo, maximizando eficiência e qualidade.

**📋 WORKFLOWS INTEGRADOS**: Colaboração predefinida entre agentes assegura que projetos complexos sejam executados com precisão e consistência.
