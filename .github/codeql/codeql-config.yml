name: "NeonPro CodeQL Config"

# Disable default queries and use custom query suites
disable-default-queries: false

# Query suites to run
queries:
  - uses: security-extended
  - uses: security-and-quality

# Paths to analyze
paths:
  - "apps/"
  - "packages/"
  - "libs/"

# Paths to ignore
paths-ignore:
  - "**/*.test.ts"
  - "**/*.test.tsx"
  - "**/*.spec.ts"
  - "**/*.spec.tsx"
  - "**/node_modules/"
  - "**/.next/"
  - "**/dist/"
  - "**/build/"
  - "**/coverage/"
  - "**/*.min.js"
  - "**/*.d.ts"
  - "**/playwright-report/"
  - "**/.turbo/"

# Healthcare-specific security configurations
query-filters:
  - exclude:
      id: js/hardcoded-credentials
      # Allow test credentials in test files
      paths:
        - "**/__tests__/**"
        - "**/*.test.*"
        - "**/*.spec.*"
        - "**/e2e/**"
  
  - exclude:
      id: js/clear-text-logging
      # Allow logging in development mode
      paths:
        - "**/dev/**"
        - "**/development/**"

# Custom queries for healthcare compliance
custom-queries:
  - name: "Healthcare Data Protection"
    description: "Detect potential LGPD/HIPAA violations"
    queries:
      - "./codeql-queries/healthcare-data-protection.ql"
  
  - name: "Medical Device Security"
    description: "ANVISA compliance checks"
    queries:
      - "./codeql-queries/medical-device-security.ql"