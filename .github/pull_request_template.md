<!-- Powered by BMAD™ Core -->

## 📋 Description

### What does this PR do?
<!-- Provide a clear, concise description of the changes -->

### Why is this change needed?
<!-- Explain the business/technical justification -->

### Related Issues
<!-- Link to related issues, stories, or tickets -->
Closes #[issue-number]
Related to #[issue-number]

## 🧪 Testing Instructions

### How to test these changes:
1. [ ] Step-by-step testing instructions
2. [ ] Expected behavior description
3. [ ] Edge cases to verify

### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] E2E tests added/updated (if applicable)

## ⚠️ Breaking Changes

<!-- If there are breaking changes, describe them here -->
- [ ] No breaking changes
- [ ] Breaking changes documented below:

### Migration Guide (if applicable)
<!-- Provide migration instructions for breaking changes -->

## 📚 Documentation

- [ ] Code comments updated
- [ ] README updated (if applicable)
- [ ] API documentation updated (if applicable)
- [ ] Architecture docs updated (if applicable)

## 🔍 Code Review Checklist

### Functionality
- [ ] Code works as intended
- [ ] Edge cases handled appropriately
- [ ] Error handling implemented
- [ ] Performance considerations addressed

### Code Quality
- [ ] Code follows project conventions
- [ ] Functions/methods are appropriately sized
- [ ] Variable/function names are descriptive
- [ ] No code duplication
- [ ] Comments explain complex logic

### Security
- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization handled
- [ ] SQL injection prevention (if applicable)

### Testing
- [ ] Adequate test coverage
- [ ] Tests are meaningful and comprehensive
- [ ] Tests pass consistently
- [ ] Mock/stub usage is appropriate

## 🚀 Deployment Checklist

- [ ] Database migrations tested
- [ ] Environment variables updated
- [ ] Feature flags configured
- [ ] Monitoring alerts configured
- [ ] Rollback plan documented

## 📊 Performance Impact

- [ ] No significant performance regression
- [ ] Load testing completed (if applicable)
- [ ] Memory usage analyzed
- [ ] Database query optimization verified

---

**Reviewer Guidelines:**
- Focus on code quality, security, and health compliance
- Verify all automated checks pass
- Ensure proper testing coverage
- Validate health regulation compliance
- Check for potential security vulnerabilities
